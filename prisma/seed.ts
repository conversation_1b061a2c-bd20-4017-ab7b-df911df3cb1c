import { PrismaClient } from '@prisma/client'
import { Faker, en } from '@faker-js/faker'

const prisma = new PrismaClient()

// Create a new Faker instance with the English locale
const faker = new Faker({ locale: [en] })

// Utility functions
const generateSlug = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '')
}

const randomChoice = <T>(array: T[]): T => {
  return array[Math.floor(Math.random() * array.length)]
}

const randomChoices = <T>(array: T[], count: number): T[] => {
  const shuffled = [...array].sort(() => 0.5 - Math.random())
  return shuffled.slice(0, Math.min(count, array.length))
}

// Seed data constants - English only
const CATEGORIES = [
  { name: 'Web Development', desc: 'Custom web applications and responsive websites' },
  { name: 'Mobile Development', desc: 'Native iOS and Android mobile applications' },
  { name: 'UI/UX Design', desc: 'User interface and user experience design services' },
  { name: 'Digital Marketing', desc: 'SEO, SEM, social media marketing and analytics' },
  { name: 'Cloud Solutions', desc: 'Cloud infrastructure and deployment services' },
  { name: 'E-commerce', desc: 'Online store development and payment integration' },
  { name: 'Consulting', desc: 'Technology strategy and business consulting' },
]

const TECHNOLOGIES = [
  { name: 'React', desc: 'Modern JavaScript library for building user interfaces' },
  { name: 'Next.js', desc: 'Full-stack React framework for production applications' },
  { name: 'TypeScript', desc: 'Strongly typed programming language built on JavaScript' },
  { name: 'Node.js', desc: 'JavaScript runtime for server-side development' },
  { name: 'PostgreSQL', desc: 'Advanced open source relational database system' },
  { name: 'Prisma', desc: 'Next-generation ORM for Node.js and TypeScript' },
  { name: 'Tailwind CSS', desc: 'Utility-first CSS framework for rapid UI development' },
  { name: 'Python', desc: 'High-level programming language for web and data science' },
  { name: 'Django', desc: 'High-level Python web framework for rapid development' },
  { name: 'AWS', desc: 'Amazon Web Services cloud computing platform' },
  { name: 'Docker', desc: 'Containerization platform for application deployment' },
  { name: 'MongoDB', desc: 'NoSQL document database for modern applications' },
]

// English company names and locations
const COMPANY_NAMES = [
  'TechCorp Solutions', 'Digital Innovations Inc', 'WebCraft Studios', 'DataFlow Systems',
  'CloudTech Enterprises', 'InnovateLab', 'SmartSolutions LLC', 'NextGen Technologies',
  'PixelPerfect Design', 'CodeCraft Development', 'StreamlineIT', 'FutureTech Partners',
  'AgileWorks', 'BrightIdeas Co', 'TechVision Group', 'DigitalEdge Solutions'
]

const CITIES = [
  'New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia',
  'San Antonio', 'San Diego', 'Dallas', 'San Jose', 'Austin', 'Jacksonville',
  'Fort Worth', 'Columbus', 'Charlotte', 'San Francisco', 'Indianapolis',
  'Seattle', 'Denver', 'Washington', 'Boston', 'Nashville', 'Baltimore',
  'Oklahoma City', 'Louisville', 'Portland', 'Las Vegas', 'Milwaukee', 'Albuquerque'
]

const STATES = [
  'California', 'Texas', 'Florida', 'New York', 'Pennsylvania', 'Illinois',
  'Ohio', 'Georgia', 'North Carolina', 'Michigan', 'New Jersey', 'Virginia',
  'Washington', 'Arizona', 'Massachusetts', 'Tennessee', 'Indiana', 'Missouri',
  'Maryland', 'Wisconsin', 'Colorado', 'Minnesota', 'South Carolina', 'Alabama'
]

const JOB_TITLES = [
  'Software Engineer', 'Senior Developer', 'Project Manager', 'UI/UX Designer',
  'Data Analyst', 'DevOps Engineer', 'Product Manager', 'Quality Assurance Engineer',
  'Business Analyst', 'Technical Lead', 'Frontend Developer', 'Backend Developer',
  'Full Stack Developer', 'Mobile Developer', 'Cloud Architect', 'Scrum Master'
]

const SERVICE_NAMES = [
  'Custom Web Application', 'E-commerce Platform', 'Mobile App Development',
  'UI/UX Design Package', 'SEO Optimization', 'Cloud Migration Service',
  'Database Design', 'API Development', 'Website Redesign', 'Digital Marketing Campaign',
  'Technical Consulting', 'Performance Optimization', 'Security Audit', 'Maintenance Package'
]

const PROJECT_NAMES = [
  'E-commerce Platform Redesign', 'Mobile Banking App', 'Customer Portal Development',
  'Inventory Management System', 'Real Estate Platform', 'Healthcare Dashboard',
  'Learning Management System', 'Social Media Analytics Tool', 'CRM Integration',
  'Payment Gateway Implementation', 'Content Management System', 'Business Intelligence Dashboard'
]

const PROJECT_STATUSES = ['PLANNING', 'IN_PROGRESS', 'COMPLETED', 'ON_HOLD']
const INVOICE_STATUSES = ['DRAFT', 'SENT', 'PAID', 'OVERDUE']
const ORDER_STATUSES = ['PENDING', 'CONFIRMED', 'IN_PROGRESS', 'COMPLETED']
const CONTRACT_STATUSES = ['DRAFT', 'PENDING', 'SIGNED', 'ACTIVE', 'COMPLETED']
const EMPLOYMENT_TYPES = ['FULL_TIME', 'PART_TIME', 'CONTRACT', 'FREELANCE']
const PAYMENT_METHODS = ['CREDIT_CARD', 'BANK_TRANSFER', 'PAYPAL', 'CHECK']

async function main() {
  console.log('🌱 Starting database seeding...')

  // Clear existing data in dependency order
  await prisma.payments.deleteMany()
  await prisma.invoiceitems.deleteMany()
  await prisma.invoices.deleteMany()
  await prisma.contracts.deleteMany()
  await prisma.orderdetails.deleteMany()
  await prisma.serviceoptionfeatures.deleteMany()
  await prisma.serviceoptions.deleteMany()
  await prisma.services.deleteMany()
  await prisma.categories.deleteMany()
  await prisma.tasks.deleteMany()
  await prisma.payrollrecords.deleteMany()
  await prisma.projectdocuments.deleteMany()
  await prisma.messages.deleteMany()
  await prisma.projecttechnologies.deleteMany()
  await prisma.projects.deleteMany()
  await prisma.orders.deleteMany()
  await prisma.testimonials.deleteMany()
  await prisma.feedbacks.deleteMany()
  await prisma.clients.deleteMany()
  await prisma.teammembers.deleteMany()
  await prisma.technologies.deleteMany()
  await prisma.jobapplications.deleteMany()
  await prisma.joblistings.deleteMany()
  await prisma.contactforms.deleteMany()
  await prisma.blogposts.deleteMany()
  await prisma.heroslides.deleteMany()
  await prisma.herosections.deleteMany()
  await prisma.aboutpagesections.deleteMany()
  await prisma.aboutpages.deleteMany()
  await prisma.legalpagesections.deleteMany()
  await prisma.legalpages.deleteMany()
  await prisma.sitesettings.deleteMany()
  await prisma.chatbotquickactions.deleteMany()
  await prisma.chatbotresponses.deleteMany()
  await prisma.chatbotkeywords.deleteMany()
  await prisma.chatbotintents.deleteMany()
  await prisma.datauploadlogs.deleteMany()
  await prisma.users.deleteMany()

  console.log('🗑️  Cleared existing data')

  // 1. Create Users (no dependencies)
  console.log('👥 Creating users...')
  const users = await Promise.all([
    prisma.users.create({
      data: {
        email: '<EMAIL>',
        firstname: 'Admin',
        lastname: 'User',
        role: 'ADMIN',
        password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hODztzuTK', // password123
        imageurl: faker.image.avatar(),
        emailverified: new Date(),
      },
    }),
    ...Array.from({ length: 5 }, () =>
      prisma.users.create({
        data: {
          email: faker.internet.email(),
          firstname: faker.person.firstName(),
          lastname: faker.person.lastName(),
          role: randomChoice(['USER', 'ADMIN']),
          password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hODztzuTK',
          imageurl: faker.image.avatar(),
          emailverified: faker.date.past(),
        },
      })
    ),
  ])

  // 2. Create Categories (no dependencies)
  console.log('📂 Creating categories...')
  const categories = await Promise.all(
    CATEGORIES.map((cat, index) =>
      prisma.categories.create({
        data: {
          categname: cat.name,
          categdesc: cat.desc,
          displayorder: index,
          isactive: true,
        },
      })
    )
  )

  // 3. Create Technologies (no dependencies)
  console.log('⚡ Creating technologies...')
  const technologies = await Promise.all(
    TECHNOLOGIES.map((tech, index) =>
      prisma.technologies.create({
        data: {
          name: tech.name,
          description: tech.desc,
          iconurl: faker.image.url({ width: 64, height: 64 }),
          displayorder: index,
          isactive: true,
        },
      })
    )
  )

  // 4. Create Team Members (no dependencies)
  console.log('👨‍💼 Creating team members...')
  const teamMembers = await Promise.all(
    Array.from({ length: 8 }, (_, index) =>
      prisma.teammembers.create({
        data: {
          name: `${faker.person.firstName()} ${faker.person.lastName()}`,
          position: randomChoice(JOB_TITLES),
          birthdate: faker.date.birthdate({ min: 25, max: 55, mode: 'age' }),
          gender: randomChoice(['Male', 'Female', 'Other']),
          maritalstatus: randomChoice(['Single', 'Married', 'Divorced']),
          hiredate: faker.date.past({ years: 5 }),
          address: `${faker.number.int({ min: 100, max: 9999 })} ${faker.location.street()}`,
          city: randomChoice(CITIES),
          state: randomChoice(STATES),
          zipcode: faker.location.zipCode(),
          country: 'United States',
          phone: faker.phone.number(),
          email: faker.internet.email(),
          salary: faker.number.float({ min: 50000, max: 150000, fractionDigits: 2 }),
          payrollmethod: randomChoice(['DIRECT_DEPOSIT', 'CHECK', 'WIRE']),
          bio: `Experienced ${randomChoice(JOB_TITLES).toLowerCase()} with expertise in modern web technologies and agile development methodologies.`,
          photourl: faker.image.avatar(),
          linkedinurl: `https://linkedin.com/in/${faker.internet.displayName().toLowerCase().replace(/\s+/g, '')}`,
          githuburl: `https://github.com/${faker.internet.displayName().toLowerCase().replace(/\s+/g, '')}`,
          displayorder: index,
          isactive: true,
        },
      })
    )
  )

  // 5. Create Clients (depends on users)
  console.log('🏢 Creating clients...')
  const clients = await Promise.all(
    Array.from({ length: 15 }, () =>
      prisma.clients.create({
        data: {
          userid: randomChoice([null, ...users.map(u => u.id)]),
          companyname: randomChoice(COMPANY_NAMES),
          contactname: `${faker.person.firstName()} ${faker.person.lastName()}`,
          contactposition: randomChoice(JOB_TITLES),
          contactemail: faker.internet.email(),
          contactphone: faker.phone.number(),
          companywebsite: `https://www.${faker.internet.domainName()}`,
          address: `${faker.number.int({ min: 100, max: 9999 })} ${faker.location.street()}`,
          city: randomChoice(CITIES),
          state: randomChoice(STATES),
          zipcode: faker.location.zipCode(),
          country: 'United States',
          logourl: faker.image.url({ width: 200, height: 200 }),
          notes: `Professional client with focus on ${randomChoice(['technology solutions', 'digital transformation', 'business growth', 'innovation', 'market expansion'])}.`,
          isactive: true,
        },
      })
    )
  )

  // 6. Create Services (depends on categories)
  console.log('🛠️ Creating services...')
  const services = await Promise.all(
    categories.flatMap((category, categoryIndex) =>
      Array.from({ length: 3 }, (_, serviceIndex) =>
        prisma.services.create({
          data: {
            categid: category.id,
            name: randomChoice(SERVICE_NAMES),
            description: `Professional ${category.categname.toLowerCase()} service with comprehensive planning, development, and support.`,
            iconclass: `fas fa-${randomChoice(['code', 'mobile', 'paint-brush', 'chart-line', 'cogs', 'laptop', 'database', 'cloud'])}`,
            price: faker.number.float({ min: 500, max: 50000, fractionDigits: 2 }),
            discountrate: faker.number.int({ min: 0, max: 20 }),
            manager: `${faker.person.firstName()} ${faker.person.lastName()}`,
            isactive: true,
            displayorder: categoryIndex * 3 + serviceIndex,
          },
        })
      )
    )
  )

  // 7. Create Service Options (depends on services)
  console.log('⚙️ Creating service options...')
  const serviceOptions = await Promise.all(
    services.flatMap(service =>
      Array.from({ length: faker.number.int({ min: 1, max: 3 }) }, () =>
        prisma.serviceoptions.create({
          data: {
            servid: service.id,
            optname: faker.commerce.productAdjective(),
            optprice: faker.number.float({ min: 100, max: 5000, fractionDigits: 2 }),
            optdiscountrate: faker.number.int({ min: 0, max: 15 }),
            optdesc: faker.lorem.sentence(),
            isactive: true,
          },
        })
      )
    )
  )

  // 8. Create Service Option Features (depends on service options)
  console.log('✨ Creating service option features...')
  await Promise.all(
    serviceOptions.flatMap(option =>
      Array.from({ length: faker.number.int({ min: 1, max: 4 }) }, () =>
        prisma.serviceoptionfeatures.create({
          data: {
            optid: option.id,
            featname: faker.commerce.productMaterial(),
            featcost: faker.number.float({ min: 50, max: 1000, fractionDigits: 2 }),
            featdiscountrate: faker.number.int({ min: 0, max: 10 }),
            featdesc: faker.lorem.sentence(),
            isincluded: faker.datatype.boolean(),
          },
        })
      )
    )
  )

  // 9. Create Orders (depends on clients and team members)
  console.log('📋 Creating orders...')
  const orders = await Promise.all(
    Array.from({ length: 20 }, (_, index) =>
      prisma.orders.create({
        data: {
          ordertitle: `${randomChoice(SERVICE_NAMES)} - Order #${String(index + 1).padStart(3, '0')}`,
          clientid: randomChoice(clients).id,
          ordermanager: randomChoice([null, ...teamMembers.map(tm => tm.id)]),
          orderdesc: `Professional service order for ${randomChoice(['web development', 'mobile application', 'digital marketing', 'consulting services', 'cloud solutions'])} with comprehensive project management and support.`,
          orderdate: faker.date.past(),
          ordertotalamount: faker.number.float({ min: 1000, max: 100000, fractionDigits: 2 }),
          orderdiscountrate: faker.number.int({ min: 0, max: 20 }),
          status: randomChoice(ORDER_STATUSES),
          notes: `Order processed and assigned to project team. Expected delivery within agreed timeline.`,
          isactive: true,
        },
      })
    )
  )

  // 10. Create Order Details (depends on orders, services, options, features)
  console.log('📝 Creating order details...')
  await Promise.all(
    orders.flatMap(order =>
      Array.from({ length: faker.number.int({ min: 1, max: 3 }) }, () =>
        prisma.orderdetails.create({
          data: {
            orderid: order.id,
            servid: randomChoice(services).id,
            optid: randomChoice([null, ...serviceOptions.map(so => so.id)]),
            costeach: faker.number.float({ min: 100, max: 10000, fractionDigits: 2 }),
            discountrate: faker.number.int({ min: 0, max: 15 }),
            comments: faker.lorem.sentence(),
            notes: faker.lorem.sentence(),
            isactive: true,
          },
        })
      )
    )
  )

  // 11. Create Projects (depends on orders, clients, team members)
  console.log('🚀 Creating projects...')
  const projects = await Promise.all(
    Array.from({ length: 12 }, (_, index) =>
      prisma.projects.create({
        data: {
          name: randomChoice(PROJECT_NAMES),
          description: `Comprehensive ${randomChoice(['web application', 'mobile solution', 'digital platform', 'software system'])} designed to enhance business operations and user experience.`,
          projgoals: `Deliver a high-quality, scalable solution that meets client requirements and exceeds expectations.`,
          projmanager: randomChoice([null, ...teamMembers.map(tm => tm.id)]),
          clientid: randomChoice([null, ...clients.map(c => c.id)]),
          orderid: randomChoice(orders).id,
          imageurl: faker.image.url({ width: 800, height: 600 }),
          projecturl: `https://project-${index + 1}.technoloway.com`,
          githuburl: `https://github.com/technoloway/project-${index + 1}`,
          tags: randomChoices(['React', 'Next.js', 'TypeScript', 'Node.js', 'PostgreSQL', 'AWS', 'Docker', 'Tailwind CSS'], 4).join(','),
          projstartdate: faker.date.past(),
          projcompletiondate: faker.date.future(),
          estimatecost: faker.number.float({ min: 5000, max: 100000, fractionDigits: 2 }),
          estimatetime: `${faker.number.int({ min: 1, max: 12 })} months`,
          estimateeffort: `${faker.number.int({ min: 100, max: 1000 })} hours`,
          status: randomChoice(PROJECT_STATUSES),
          isfeatured: faker.datatype.boolean(),
          ispublic: faker.datatype.boolean(),
          displayorder: index,
        },
      })
    )
  )

  // 12. Create Project Technologies (depends on projects and technologies)
  console.log('🔧 Creating project technologies...')
  await Promise.all(
    projects.flatMap(project =>
      randomChoices(technologies, faker.number.int({ min: 2, max: 5 })).map(tech =>
        prisma.projecttechnologies.create({
          data: {
            projectsid: project.id,
            technologiesid: tech.id,
          },
        })
      )
    )
  )

  // 13. Create Contracts (depends on projects, clients, orders, team members)
  console.log('📄 Creating contracts...')
  const contracts = await Promise.all(
    Array.from({ length: 8 }, () => {
      const project = randomChoice(projects)
      return prisma.contracts.create({
        data: {
          contname: faker.commerce.productName(),
          projid: project.id,
          clientid: project.clientid || randomChoice(clients).id,
          orderid: project.orderid,
          contmanager: randomChoice([null, ...teamMembers.map(tm => tm.id)]),
          contservtype: randomChoice(['Development', 'Consulting', 'Design', 'Support']),
          contlang: 'English',
          agreementdesc: faker.lorem.paragraph(),
          contvalue: faker.number.float({ min: 10000, max: 200000, fractionDigits: 2 }),
          contvaluecurr: 'USD',
          billingtype: randomChoice(['Fixed', 'Hourly', 'Monthly']),
          nextbilldate: faker.date.future(),
          contsignmethod: randomChoice(['Electronic', 'Physical', 'Digital']),
          contsigneddate: faker.date.past(),
          contexecuteddate: faker.date.past(),
          contexpirydate: faker.date.future(),
          contstatus: randomChoice(CONTRACT_STATUSES),
          lastupdateuser: randomChoice([null, ...teamMembers.map(tm => tm.id)]),
          comments: faker.lorem.sentence(),
          notes: faker.lorem.paragraph(),
        },
      })
    })
  )

  // 14. Create Invoices (depends on clients, contracts, orders, projects)
  console.log('💰 Creating invoices...')
  const invoices = await Promise.all(
    Array.from({ length: 15 }, () => {
      const contract = randomChoice(contracts)
      return prisma.invoices.create({
        data: {
          duedate: faker.date.future(),
          subtotal: faker.number.float({ min: 1000, max: 50000, fractionDigits: 2 }),
          taxrate: faker.number.float({ min: 0, max: 0.15, fractionDigits: 4 }),
          taxamount: faker.number.float({ min: 100, max: 5000, fractionDigits: 2 }),
          totalamount: faker.number.float({ min: 1100, max: 55000, fractionDigits: 2 }),
          status: randomChoice(INVOICE_STATUSES),
          description: faker.lorem.sentence(),
          clientid: contract.clientid,
          contid: contract.id,
          orderid: contract.orderid,
          projectid: randomChoice([null, contract.projid]),
          paidat: faker.datatype.boolean() ? faker.date.past() : null,
        },
      })
    })
  )

  // 15. Create Invoice Items (depends on invoices)
  console.log('📋 Creating invoice items...')
  await Promise.all(
    invoices.flatMap(invoice =>
      Array.from({ length: faker.number.int({ min: 1, max: 5 }) }, () =>
        prisma.invoiceitems.create({
          data: {
            description: faker.commerce.productName(),
            quantity: faker.number.float({ min: 1, max: 10, fractionDigits: 2 }),
            unitprice: faker.number.float({ min: 50, max: 5000, fractionDigits: 2 }),
            totalprice: faker.number.float({ min: 50, max: 50000, fractionDigits: 2 }),
            invoiceid: invoice.id,
          },
        })
      )
    )
  )

  // 16. Create Payments (depends on invoices)
  console.log('💳 Creating payments...')
  await Promise.all(
    invoices
      .filter(() => faker.datatype.boolean())
      .flatMap(invoice =>
        Array.from({ length: faker.number.int({ min: 1, max: 2 }) }, () =>
          prisma.payments.create({
            data: {
              amount: faker.number.float({ min: 100, max: Number(invoice.totalamount), fractionDigits: 2 }),
              paymentdate: faker.date.past(),
              paymentmethod: randomChoice(PAYMENT_METHODS),
              status: randomChoice(['Completed', 'Pending', 'Failed']),
              notes: faker.lorem.sentence(),
              invoiceid: invoice.id,
            },
          })
        )
      )
  )

  // 17. Create Testimonials (depends on clients)
  console.log('⭐ Creating testimonials...')
  const testimonialTexts = [
    "Technoloway delivered an exceptional web application that exceeded our expectations. Their team's expertise and attention to detail made the entire process smooth and professional.",
    "Working with Technoloway was a game-changer for our business. They transformed our outdated system into a modern, efficient platform that our customers love.",
    "The mobile app developed by Technoloway has significantly improved our customer engagement. Their innovative approach and technical skills are truly impressive.",
    "Technoloway's consulting services helped us streamline our operations and implement best practices. Their insights were invaluable to our digital transformation.",
    "Outstanding work on our e-commerce platform! Technoloway's team delivered on time and within budget, with excellent ongoing support.",
    "The UI/UX design created by Technoloway is both beautiful and functional. Our users have provided overwhelmingly positive feedback.",
    "Technoloway's cloud migration services were seamless and efficient. They handled everything professionally and kept us informed throughout the process.",
    "Excellent project management and communication throughout our development project. Technoloway truly understands client needs and delivers results.",
    "The custom software solution developed by Technoloway has revolutionized our workflow. Highly recommend their services to any business.",
    "Professional, reliable, and innovative - Technoloway exceeded our expectations in every aspect of our project collaboration."
  ]

  await Promise.all(
    Array.from({ length: 10 }, (_, index) => {
      const client = randomChoice(clients)
      return prisma.testimonials.create({
        data: {
          clientid: client.id,
          clientname: client.contactname,
          clienttitle: client.contactposition || randomChoice(JOB_TITLES),
          clientcompany: client.companyname,
          clientphotourl: faker.image.avatar(),
          content: testimonialTexts[index],
          rating: faker.number.int({ min: 4, max: 5 }),
          isfeatured: faker.datatype.boolean(),
          displayorder: index,
        },
      })
    })
  )

  // 18. Create Blog Posts (depends on users)
  console.log('📝 Creating blog posts...')
  const blogTitles = [
    'Getting Started with React and TypeScript',
    'Building Scalable Web Applications with Next.js',
    'Modern UI/UX Design Principles for 2024',
    'Cloud Migration Strategies for Small Businesses',
    'The Future of Mobile App Development',
    'Best Practices for Database Design',
    'Implementing DevOps in Your Development Workflow',
    'Digital Marketing Trends and Strategies'
  ]

  await Promise.all(
    Array.from({ length: 8 }, (_, index) =>
      prisma.blogposts.create({
        data: {
          authorid: randomChoice(users).id.toString(),
          title: blogTitles[index],
          content: `This comprehensive guide explores the latest trends and best practices in modern web development. We'll cover essential concepts, practical examples, and real-world applications that will help you build better software solutions.\n\nIn today's rapidly evolving technology landscape, staying up-to-date with the latest tools and methodologies is crucial for success. This article provides insights from industry experts and practical advice for developers at all levels.\n\nWhether you're just starting your development journey or looking to enhance your existing skills, this guide offers valuable information and actionable strategies that you can implement in your projects immediately.`,
          slug: generateSlug(blogTitles[index]),
          featuredimageurl: faker.image.url({ width: 800, height: 600 }),
          excerpt: `Learn about the latest trends and best practices in ${randomChoice(['web development', 'mobile applications', 'digital marketing', 'cloud computing', 'software engineering'])}.`,
          ispublished: faker.datatype.boolean(),
          publishedat: faker.datatype.boolean() ? faker.date.past() : null,
          categories: randomChoices(['Technology', 'Development', 'Design', 'Business', 'Tutorial'], 2).join(','),
          tags: randomChoices(['React', 'Next.js', 'TypeScript', 'Node.js', 'UI/UX', 'Mobile', 'Cloud', 'DevOps'], 3).join(','),
        },
      })
    )
  )

  // 19. Create Job Listings (no dependencies)
  console.log('💼 Creating job listings...')
  const jobListings = await Promise.all(
    Array.from({ length: 6 }, () =>
      prisma.joblistings.create({
        data: {
          title: faker.person.jobTitle(),
          description: faker.lorem.paragraphs(3),
          requirements: faker.lorem.paragraphs(2),
          location: faker.location.city(),
          employmenttype: randomChoice(EMPLOYMENT_TYPES),
          salarymin: faker.number.float({ min: 40000, max: 80000, fractionDigits: 2 }),
          salarymax: faker.number.float({ min: 80000, max: 150000, fractionDigits: 2 }),
          salarycurrency: 'USD',
          isremote: faker.datatype.boolean(),
          isactive: true,
          expiresat: faker.date.future(),
        },
      })
    )
  )

  // 20. Create Job Applications (depends on job listings)
  console.log('📄 Creating job applications...')
  await Promise.all(
    jobListings.flatMap(job =>
      Array.from({ length: faker.number.int({ min: 2, max: 8 }) }, () =>
        prisma.jobapplications.create({
          data: {
            applicantname: faker.person.fullName(),
            applicantemail: faker.internet.email(),
            applicantphone: faker.phone.number(),
            resumeurl: faker.internet.url(),
            coverletter: faker.lorem.paragraphs(2),
            status: randomChoice(['PENDING', 'REVIEWED', 'INTERVIEW', 'HIRED', 'REJECTED']),
            notes: faker.lorem.sentence(),
            joblistingid: job.id,
          },
        })
      )
    )
  )

  // 21. Create Contact Forms (no dependencies)
  console.log('📞 Creating contact forms...')
  await Promise.all(
    Array.from({ length: 12 }, () =>
      prisma.contactforms.create({
        data: {
          name: faker.person.fullName(),
          email: faker.internet.email(),
          phone: faker.phone.number(),
          subject: faker.lorem.sentence(),
          message: faker.lorem.paragraphs(2),
          isread: faker.datatype.boolean(),
          readat: faker.datatype.boolean() ? faker.date.past() : null,
          status: randomChoice(['New', 'In Progress', 'Resolved', 'Closed']),
        },
      })
    )
  )

  // 22. Create Hero Sections (no dependencies)
  console.log('🎯 Creating hero sections...')
  const heroSections = await Promise.all(
    Array.from({ length: 3 }, () =>
      prisma.herosections.create({
        data: {
          title: faker.lorem.sentence(),
          metadescription: faker.lorem.paragraph(),
          metakeywords: randomChoices(['technology', 'development', 'software', 'web', 'mobile'], 3).join(','),
          pagename: randomChoice(['Home', 'About', 'Services', 'Contact']),
          maintitle: faker.lorem.sentence(),
          mainsubtitle: faker.lorem.sentence(),
          maindescription: faker.lorem.paragraph(),
          primarybuttontext: 'Get Started',
          primarybuttonurl: '/contact',
          secondarybuttontext: 'Learn More',
          secondarybuttonurl: '/about',
          enableslideshow: 1,
          slideshowspeed: 5000,
          autoplay: 1,
          showdots: 1,
          showarrows: 1,
          enablefloatingelements: 1,
          isactive: true,
          modifiedby: 'admin',
        },
      })
    )
  )

  // 23. Create Hero Slides (depends on hero sections)
  console.log('🎬 Creating hero slides...')
  await Promise.all(
    heroSections.flatMap(section =>
      Array.from({ length: faker.number.int({ min: 2, max: 4 }) }, (_, index) =>
        prisma.heroslides.create({
          data: {
            herosectionid: section.id,
            content: faker.lorem.paragraph(),
            mediatype: randomChoice(['image', 'video']),
            imageurl: faker.image.url({ width: 1920, height: 1080 }),
            videourl: faker.datatype.boolean() ? faker.internet.url() : null,
            mediaalt: faker.lorem.sentence(),
            videoautoplay: 1,
            videomuted: 1,
            videoloop: 1,
            videocontrols: 0,
            buttontext: 'Learn More',
            buttonurl: faker.internet.url(),
            displayorder: index,
            isactive: true,
            animationtype: randomChoice(['fade', 'slide', 'zoom']),
            duration: 5000,
          },
        })
      )
    )
  )

  // 24. Create About Pages (no dependencies)
  console.log('📖 Creating about pages...')
  const aboutPages = await Promise.all(
    Array.from({ length: 2 }, () =>
      prisma.aboutpages.create({
        data: {
          title: faker.lorem.sentence(),
          metadescription: faker.lorem.paragraph(),
          metakeywords: randomChoices(['about', 'company', 'team', 'mission'], 3).join(','),
          herotitle: faker.lorem.sentence(),
          herosubtitle: faker.lorem.sentence(),
          heroimageurl: faker.image.url({ width: 1200, height: 600 }),
          storytitle: faker.lorem.sentence(),
          storysubtitle: faker.lorem.sentence(),
          storycontent: faker.lorem.paragraphs(3),
          storyimageurl: faker.image.url({ width: 800, height: 600 }),
          stat1number: faker.number.int({ min: 50, max: 500 }).toString(),
          stat1label: 'Projects Completed',
          stat2number: faker.number.int({ min: 10, max: 100 }).toString(),
          stat2label: 'Happy Clients',
          stat3number: faker.number.int({ min: 5, max: 50 }).toString(),
          stat3label: 'Team Members',
          stat4number: faker.number.int({ min: 1, max: 10 }).toString(),
          stat4label: 'Years Experience',
          missiontitle: 'Our Mission',
          missioncontent: faker.lorem.paragraph(),
          visiontitle: 'Our Vision',
          visioncontent: faker.lorem.paragraph(),
          valuestitle: 'Our Values',
          valuescontent: faker.lorem.paragraph(),
          ctatitle: faker.lorem.sentence(),
          ctasubtitle: faker.lorem.sentence(),
          ctaprimarybuttontext: 'Get Started',
          ctaprimarybuttonurl: '/contact',
          ctasecondarybuttontext: 'Learn More',
          ctasecondarybuttonurl: '/services',
          isactive: true,
          modifiedby: 'admin',
        },
      })
    )
  )

  // 25. Create About Page Sections (depends on about pages)
  console.log('📑 Creating about page sections...')
  await Promise.all(
    aboutPages.flatMap(page =>
      Array.from({ length: faker.number.int({ min: 2, max: 4 }) }, (_, index) =>
        prisma.aboutpagesections.create({
          data: {
            aboutpageid: page.id,
            title: faker.lorem.sentence(),
            content: faker.lorem.paragraphs(2),
            iconclass: `fas fa-${randomChoice(['star', 'heart', 'lightbulb', 'rocket', 'shield'])}`,
            imageurl: faker.image.url({ width: 400, height: 300 }),
            displayorder: index,
            isactive: true,
            sectiontype: randomChoice(['content', 'feature', 'testimonial']),
          },
        })
      )
    )
  )

  // 26. Create Legal Pages (no dependencies)
  console.log('⚖️ Creating legal pages...')
  const legalPages = await Promise.all([
    prisma.legalpages.create({
      data: {
        title: 'Privacy Policy',
        slug: 'privacy-policy',
        metadescription: 'Our privacy policy explains how we collect and use your data',
        content: faker.lorem.paragraphs(10),
        isactive: true,
        displayorder: 1,
        lastmodified: new Date(),
        modifiedby: 'admin',
      },
    }),
    prisma.legalpages.create({
      data: {
        title: 'Terms of Service',
        slug: 'terms-of-service',
        metadescription: 'Terms and conditions for using our services',
        content: faker.lorem.paragraphs(8),
        isactive: true,
        displayorder: 2,
        lastmodified: new Date(),
        modifiedby: 'admin',
      },
    }),
  ])

  // 27. Create Legal Page Sections (depends on legal pages)
  console.log('📋 Creating legal page sections...')
  await Promise.all(
    legalPages.flatMap(page =>
      Array.from({ length: faker.number.int({ min: 3, max: 6 }) }, (_, index) =>
        prisma.legalpagesections.create({
          data: {
            legalpageid: page.id,
            title: faker.lorem.sentence(),
            content: faker.lorem.paragraphs(3),
            iconclass: `fas fa-${randomChoice(['gavel', 'shield', 'lock', 'eye', 'file'])}`,
            displayorder: index,
            isactive: true,
          },
        })
      )
    )
  )

  // 28. Create Site Settings (no dependencies)
  console.log('⚙️ Creating site settings...')
  const siteSettingsData = [
    { key: 'site_name', value: 'Technoloway', category: 'GENERAL', description: 'Website name' },
    { key: 'site_description', value: 'Leading software development company', category: 'GENERAL', description: 'Website description' },
    { key: 'contact_email', value: '<EMAIL>', category: 'CONTACT', description: 'Main contact email' },
    { key: 'contact_phone', value: '+****************', category: 'CONTACT', description: 'Main contact phone' },
    { key: 'company_address', value: '123 Tech Street, Silicon Valley, CA 94000', category: 'CONTACT', description: 'Company address' },
    { key: 'social_facebook', value: 'https://facebook.com/technoloway', category: 'SOCIAL', description: 'Facebook URL' },
    { key: 'social_twitter', value: 'https://twitter.com/technoloway', category: 'SOCIAL', description: 'Twitter URL' },
    { key: 'social_linkedin', value: 'https://linkedin.com/company/technoloway', category: 'SOCIAL', description: 'LinkedIn URL' },
    { key: 'social_github', value: 'https://github.com/technoloway', category: 'SOCIAL', description: 'GitHub URL' },
    { key: 'business_hours', value: 'Monday - Friday: 9:00 AM - 6:00 PM', category: 'GENERAL', description: 'Business hours' },
  ]

  await Promise.all(
    siteSettingsData.map(setting =>
      prisma.sitesettings.create({
        data: {
          key: setting.key,
          value: setting.value,
          category: setting.category,
          description: setting.description,
          ispublic: true,
          isactive: true,
        },
      })
    )
  )

  // 29. Create additional entities for completeness
  console.log('🔧 Creating additional entities...')

  // Create Tasks (depends on projects and team members)
  await Promise.all(
    projects.flatMap(project =>
      Array.from({ length: faker.number.int({ min: 2, max: 6 }) }, () =>
        prisma.tasks.create({
          data: {
            projno: project.id,
            teammemberid: randomChoice(teamMembers).id,
            taskdesc: faker.lorem.sentence(),
            taskstartdate: faker.date.past(),
            taskenddate: faker.date.future(),
            workhours: faker.number.int({ min: 8, max: 40 }),
            payrate: faker.number.float({ min: 25, max: 150, fractionDigits: 2 }),
            status: randomChoice(['Active', 'Completed', 'On Hold']),
            notes: faker.lorem.sentence(),
          },
        })
      )
    )
  )

  // Create Feedbacks (depends on clients and projects)
  await Promise.all(
    Array.from({ length: 8 }, () =>
      prisma.feedbacks.create({
        data: {
          subject: faker.lorem.sentence(),
          comment: faker.lorem.paragraph(),
          feedbacktype: randomChoice(['Bug Report', 'Feature Request', 'General', 'Complaint']),
          rating: faker.number.int({ min: 1, max: 5 }),
          clientname: faker.person.fullName(),
          clientemail: faker.internet.email(),
          priority: randomChoice(['Low', 'Medium', 'High', 'Critical']),
          status: randomChoice(['New', 'In Progress', 'Resolved', 'Closed']),
          isread: faker.datatype.boolean(),
          readat: faker.datatype.boolean() ? faker.date.past() : null,
          resolvedat: faker.datatype.boolean() ? faker.date.past() : null,
          adminresponse: faker.datatype.boolean() ? faker.lorem.paragraph() : null,
          adminname: faker.datatype.boolean() ? faker.person.fullName() : null,
          responsedate: faker.datatype.boolean() ? faker.date.past() : null,
          clientid: randomChoice([null, ...clients.map(c => c.id)]),
          projectid: randomChoice([null, ...projects.map(p => p.id)]),
          ispublic: faker.datatype.boolean(),
        },
      })
    )
  )

  // Create Messages (depends on projects)
  await Promise.all(
    projects.flatMap(project =>
      Array.from({ length: faker.number.int({ min: 3, max: 8 }) }, () =>
        prisma.messages.create({
          data: {
            content: faker.lorem.paragraph(),
            sendername: faker.person.fullName(),
            senderrole: randomChoice(['CLIENT', 'ADMIN', 'DEVELOPER']),
            senderid: faker.string.uuid(),
            isread: faker.datatype.boolean(),
            readat: faker.datatype.boolean() ? faker.date.past() : null,
            projectid: project.id,
          },
        })
      )
    )
  )

  console.log('✅ All entities created successfully')
  console.log('🎉 Database seeding completed successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
