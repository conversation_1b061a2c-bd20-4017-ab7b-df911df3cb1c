import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, 
  successResponse, 
  paginatedResponse,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest,
  validateMethod,
  requireAdmin
} from '@/lib/api-utils'
import { createServiceSchema, updateServiceSchema } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/services - List all active services (public endpoint)
export const GET = withErrorHandler(async (request: NextRequest) => {
  const { page, limit, search, sortBy, sortOrder, categoryId } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build where clause for active services only (public endpoint)
  const where: any = { isactive: true }

  // Add category filter
  if (categoryId) {
    where.categoryId = categoryId
  }

  // Add search functionality
  if (search) {
    Object.assign(where, buildSearchQuery(search, ['name', 'description', 'manager']))
  }

  // Build orderBy clause
  const orderBy: any = {}
  if (sortBy) {
    orderBy[sortBy] = sortOrder || 'asc'
  } else {
    orderBy.displayorder = 'asc'
  }

  const [services, total] = await Promise.all([
    prisma.services.findMany({
      where,
      skip,
      take,
      orderBy,
      include: {
        categories: {
          select: {
            id: true,
            categname: true,
            categdesc: true,
          },
        },
        serviceoptions: {
          where: { isactive: true },
          include: {
            serviceoptionfeatures: true,
          },
          orderBy: {
            optprice: 'asc',
          },
        },
        _count: {
          select: {
            orderdetails: true,
            serviceoptions: true,
          },
        },
      },
    }),
    prisma.services.count({ where }),
  ])

  // Transform the data for frontend
  const transformedServices = services.map(service => transformFromDbFields.service(service))

  return paginatedResponse(transformedServices, page, take, total, 'Services retrieved successfully')
})

// POST /api/services - Create a new service
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['POST'])
  
  const validate = validateRequest(createServiceSchema)
  const data = await validate(request)

  // Check if category exists
  const category = await prisma.categories.findUnique({
    where: { id: data.categoryId },
  })

  if (!category) {
    throw new Error('Category not found')
  }

  const service = await prisma.services.create({
    data,
    include: {
      category: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  })

  return successResponse(service, 'Service created successfully', 201)
})

// PUT /api/services - Bulk update services (admin only)
export const PUT = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['PUT'])
  
  const body = await request.json()
  const { ids, data } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid service IDs provided')
  }

  const validate = validateRequest(updateServiceSchema)
  const updateData = await validate({ json: () => data } as NextRequest)

  const updatedServices = await prisma.services.updateMany({
    where: {
      id: {
        in: ids,
      },
    },
    data: updateData,
  })

  return successResponse(
    { count: updatedServices.count },
    `${updatedServices.count} services updated successfully`
  )
})

// DELETE /api/services - Bulk delete services (admin only)
export const DELETE = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['DELETE'])
  
  const body = await request.json()
  const { ids } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid service IDs provided')
  }

  // Check if any services are being used in projects or orders
  const servicesInUse = await prisma.services.findMany({
    where: {
      id: { in: ids },
      OR: [
        { projects: { some: {} } },
        { orderDetails: { some: {} } },
      ],
    },
    select: { id: true, name: true },
  })

  if (servicesInUse.length > 0) {
    const serviceNames = servicesInUse.map(s => s.name).join(', ')
    throw new Error(
      `Cannot delete services that are in use: ${serviceNames}. Please remove them from projects and orders first.`
    )
  }

  const deletedServices = await prisma.services.deleteMany({
    where: {
      id: {
        in: ids,
      },
    },
  })

  return successResponse(
    { count: deletedServices.count },
    `${deletedServices.count} services deleted successfully`
  )
})
