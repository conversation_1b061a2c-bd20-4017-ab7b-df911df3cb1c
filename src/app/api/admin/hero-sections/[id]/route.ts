import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/hero-sections/[id] - Get a specific hero section
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const heroSection = await prisma.herosections.findUnique({
    where: { id: BigInt(id) },
    include: {
      heroslides: {
        orderBy: { displayorder: 'asc' }
      }
    }
  })

  if (!heroSection) {
    throw new ApiError('Hero section not found', 404)
  }

  // Transform the data for frontend
  const transformedHeroSection = transformFromDbFields.heroSection(heroSection)

  return successResponse(transformedHeroSection)
})

// PUT /api/admin/hero-sections/[id] - Update a hero section
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const validate = validateRequest(schemas.heroSection.update)
  const validatedData = await validate(request)

  // Check if hero section exists
  const existingHeroSection = await prisma.herosections.findUnique({
    where: { id: BigInt(id) },
  })

  if (!existingHeroSection) {
    throw new ApiError('Hero section not found', 404)
  }

  // Transform data to database format
  const dbData = transformToDbFields.heroSection(validatedData)

  const heroSection = await prisma.herosections.update({
    where: { id: BigInt(id) },
    data: dbData,
    include: {
      heroslides: {
        orderBy: { displayorder: 'asc' }
      }
    }
  })

  // Transform back to frontend format
  const transformedHeroSection = transformFromDbFields.heroSection(heroSection)

  return successResponse(transformedHeroSection, 'Hero section updated successfully')
})

// DELETE /api/admin/hero-sections/[id] - Delete a hero section
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  // Check if hero section exists
  const existingHeroSection = await prisma.herosections.findUnique({
    where: { id: BigInt(id) },
  })

  if (!existingHeroSection) {
    throw new ApiError('Hero section not found', 404)
  }

  // First delete all associated slides
  await prisma.heroslides.deleteMany({
    where: { herosectionid: BigInt(id) }
  })

  // Then delete the hero section
  await prisma.herosections.delete({
    where: { id: BigInt(id) }
  })

  return successResponse(null, 'Hero section deleted successfully')
})
