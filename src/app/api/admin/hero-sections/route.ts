import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, 
  successResponse,
  paginatedResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/hero-sections - Get all hero sections with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build search query
  const searchQuery = buildSearchQuery(search, ['title', 'subtitle', 'description', 'pageName'])
  
  // Build sort query with correct default field
  const sortQuery = buildSortQuery(sortBy === 'createdAt' ? 'createdat' : sortBy, sortOrder)

  // Get hero sections with pagination
  const [heroSections, total] = await Promise.all([
    prisma.herosections.findMany({
      where: searchQuery,
      include: {
        heroslides: {
          orderBy: { displayorder: 'asc' }
        }
      },
      orderBy: sortQuery,
      skip,
      take,
    }),
    prisma.herosections.count({ where: searchQuery })
  ])

  // Transform the data for frontend
  const transformedHeroSections = heroSections.map(section => transformFromDbFields.heroSection(section))

  return paginatedResponse(transformedHeroSections, page, limit, total)
})

// POST /api/admin/hero-sections - Create a new hero section
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const validate = validateRequest(schemas.heroSection.create)
  const validatedData = await validate(request)

  // Transform data to database format
  const dbData = transformToDbFields.heroSection(validatedData)

  const heroSection = await prisma.herosections.create({
    data: dbData,
    include: {
      heroslides: {
        orderBy: { displayorder: 'asc' }
      }
    }
  })

  // Transform back to frontend format
  const transformedHeroSection = transformFromDbFields.heroSection(heroSection)

  return successResponse(transformedHeroSection, 'Hero section created successfully', 201)
})
