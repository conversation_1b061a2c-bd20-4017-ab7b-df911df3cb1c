import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/hero-slides/[id] - Get a specific hero slide
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const heroSlide = await prisma.heroslides.findUnique({
    where: { id },
    include: {
      heroSection: true
    }
  })

  if (!heroSlide) {
    throw new ApiError('Hero slide not found', 404)
  }

  return successResponse(heroSlide)
})

// PUT /api/admin/hero-slides/[id] - Update a hero slide
export const PUT = withError<PERSON><PERSON><PERSON>(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const validate = validateRequest(schemas.heroSlide.update)
  const data = await validate(request)

  // Check if hero slide exists
  const existingSlide = await prisma.heroslides.findUnique({
    where: { id },
  })

  if (!existingSlide) {
    throw new ApiError('Hero slide not found', 404)
  }

  const heroSlide = await prisma.heroslides.update({
    where: { id },
    data,
    include: {
      heroSection: true
    }
  })

  return successResponse(heroSlide, 'Hero slide updated successfully')
})

// DELETE /api/admin/hero-slides/[id] - Delete a hero slide
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  // Check if hero slide exists
  const existingSlide = await prisma.heroslides.findUnique({
    where: { id },
  })

  if (!existingSlide) {
    throw new ApiError('Hero slide not found', 404)
  }

  await prisma.heroslides.delete({
    where: { id }
  })

  return successResponse(null, 'Hero slide deleted successfully')
})
