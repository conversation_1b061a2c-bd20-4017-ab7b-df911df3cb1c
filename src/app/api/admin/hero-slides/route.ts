import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  withError<PERSON><PERSON><PERSON>, 
  successResponse,
  paginatedResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/hero-slides - Get all hero slides with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build search query
  const searchQuery = buildSearchQuery(search, ['title', 'subtitle', 'description'])
  
  // Build sort query
  const sortQuery = buildSortQuery(sortBy, sortOrder)

  // Get hero slides with pagination
  const [heroSlides, total] = await Promise.all([
    prisma.heroslides.findMany({
      where: searchQuery,
      include: {
        heroSection: true
      },
      orderBy: sortQuery,
      skip,
      take,
    }),
    prisma.heroslides.count({ where: searchQuery })
  ])

  return paginatedResponse(heroSlides, page, limit, total)
})

// POST /api/admin/hero-slides - Create a new hero slide
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const validate = validateRequest(schemas.heroSlide.create)
  const data = await validate(request)

  const heroSlide = await prisma.heroslides.create({
    data,
    include: {
      heroSection: true
    }
  })

  return successResponse(heroSlide, 'Hero slide created successfully', 201)
})
