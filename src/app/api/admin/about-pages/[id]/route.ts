import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/about-pages/[id] - Get a specific about page
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const aboutPage = await prisma.aboutpages.findUnique({
    where: { id },
    include: {
      sections: {
        orderBy: { displayorder: 'asc' }
      }
    }
  })

  if (!aboutPage) {
    throw new ApiError('About page not found', 404)
  }

  return successResponse(aboutPage)
})

// PUT /api/admin/about-pages/[id] - Update an about page
export const PUT = with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const validate = validateRequest(schemas.aboutPage.update)
  const data = await validate(request)

  // Check if about page exists
  const existingPage = await prisma.aboutpages.findUnique({
    where: { id },
  })

  if (!existingPage) {
    throw new ApiError('About page not found', 404)
  }

  const aboutPage = await prisma.aboutpages.update({
    where: { id },
    data,
    include: {
      sections: {
        orderBy: { displayorder: 'asc' }
      }
    }
  })

  return successResponse(aboutPage, 'About page updated successfully')
})

// DELETE /api/admin/about-pages/[id] - Delete an about page
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  // Check if about page exists
  const existingPage = await prisma.aboutpages.findUnique({
    where: { id },
  })

  if (!existingPage) {
    throw new ApiError('About page not found', 404)
  }

  // First delete all associated sections
  await prisma.aboutpagesSection.deleteMany({
    where: { aboutPageId: id }
  })

  // Then delete the about page
  await prisma.aboutpages.delete({
    where: { id }
  })

  return successResponse(null, 'About page deleted successfully')
})
