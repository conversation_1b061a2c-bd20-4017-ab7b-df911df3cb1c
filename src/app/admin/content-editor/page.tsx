'use client'

import React, { useState, useEffect } from 'react'
import {
  PlusIcon,
  TrashIcon,
  EyeIcon,
  DocumentTextIcon,
  PhotoIcon,
  Cog6ToothIcon,
  CheckIcon,
  XMarkIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  BriefcaseIcon,
  CpuChipIcon,
  UserGroupIcon,
  PencilSquareIcon,
  PhoneIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline'

// Import types from API
import type { ContentData, ContentPage, ContentSection, ContentSlide } from '@/app/api/content/route'
import HeroCarousel, { HeroCarouselPreview } from '@/components/admin/HeroCarousel'

export default function ContentEditorPage() {
  const [contentData, setContentData] = useState<ContentData | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [selectedPage, setSelectedPage] = useState<string>('home')
  const [selectedSection, setSelectedSection] = useState<string | null>(null)
  const [previewSlide, setPreviewSlide] = useState<ContentSlide | null>(null)
  const [showCarouselPreview, setShowCarouselPreview] = useState(false)
  const [previewSection, setPreviewSection] = useState<ContentSection | null>(null)
  const [hasChanges, setHasChanges] = useState(false)

  // Load content data
  useEffect(() => {
    loadContent()
  }, [])

  const loadContent = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/content')
      if (!response.ok) throw new Error('Failed to load content')
      const data = await response.json()
      setContentData(data.data)
    } catch (error) {
      console.error('Error loading content:', error)
    } finally {
      setLoading(false)
    }
  }

  const saveContent = async () => {
    if (!contentData) return
    
    try {
      setSaving(true)
      const response = await fetch('/api/content', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ pages: contentData.pages })
      })
      
      if (!response.ok) throw new Error('Failed to save content')
      
      setHasChanges(false)
      alert('Content saved successfully!')
    } catch (error) {
      console.error('Error saving content:', error)
      alert('Failed to save content')
    } finally {
      setSaving(false)
    }
  }

  const updateSection = (pageId: string, sectionId: string, updates: Partial<ContentSection>) => {
    if (!contentData) return
    
    const updatedPages = contentData.pages.map(page => {
      if (page.id === pageId) {
        return {
          ...page,
          sections: page.sections.map(section => 
            section.id === sectionId ? { ...section, ...updates } : section
          )
        }
      }
      return page
    })
    
    setContentData({ ...contentData, pages: updatedPages })
    setHasChanges(true)
  }

  const addSlide = (pageId: string, sectionId: string) => {
    if (!contentData) return
    
    const newSlide: ContentSlide = {
      id: 'new',
      title: 'New Slide Title',
      subtitle: 'New slide subtitle',
      buttonText: 'Learn More',
      buttonUrl: '#',
      imageUrl: '/images/hero-bg.jpg',
      displayOrder: 0,
      isActive: true
    }
    
    const updatedPages = contentData.pages.map(page => {
      if (page.id === pageId) {
        return {
          ...page,
          sections: page.sections.map(section => {
            if (section.id === sectionId) {
              const slides = section.slides || []
              newSlide.displayOrder = slides.length
              return { ...section, slides: [...slides, newSlide] }
            }
            return section
          })
        }
      }
      return page
    })
    
    setContentData({ ...contentData, pages: updatedPages })
    setHasChanges(true)
  }

  const removeSlide = (pageId: string, sectionId: string, slideIndex: number) => {
    if (!contentData) return
    
    const updatedPages = contentData.pages.map(page => {
      if (page.id === pageId) {
        return {
          ...page,
          sections: page.sections.map(section => {
            if (section.id === sectionId) {
              const slides = section.slides || []
              return { 
                ...section, 
                slides: slides.filter((_, index) => index !== slideIndex)
              }
            }
            return section
          })
        }
      }
      return page
    })
    
    setContentData({ ...contentData, pages: updatedPages })
    setHasChanges(true)
  }

  const updateSlide = (pageId: string, sectionId: string, slideIndex: number, updates: Partial<ContentSlide>) => {
    if (!contentData) return
    
    const updatedPages = contentData.pages.map(page => {
      if (page.id === pageId) {
        return {
          ...page,
          sections: page.sections.map(section => {
            if (section.id === sectionId) {
              const slides = section.slides || []
              return {
                ...section,
                slides: slides.map((slide, index) => 
                  index === slideIndex ? { ...slide, ...updates } : slide
                )
              }
            }
            return section
          })
        }
      }
      return page
    })
    
    setContentData({ ...contentData, pages: updatedPages })
    setHasChanges(true)
  }

  const moveSlide = (pageId: string, sectionId: string, slideIndex: number, direction: 'up' | 'down') => {
    if (!contentData) return
    
    const updatedPages = contentData.pages.map(page => {
      if (page.id === pageId) {
        return {
          ...page,
          sections: page.sections.map(section => {
            if (section.id === sectionId) {
              const slides = [...(section.slides || [])]
              const newIndex = direction === 'up' ? slideIndex - 1 : slideIndex + 1
              
              if (newIndex >= 0 && newIndex < slides.length) {
                [slides[slideIndex], slides[newIndex]] = [slides[newIndex], slides[slideIndex]]
                // Update display orders
                slides.forEach((slide, index) => {
                  slide.displayOrder = index
                })
              }
              
              return { ...section, slides }
            }
            return section
          })
        }
      }
      return page
    })
    
    setContentData({ ...contentData, pages: updatedPages })
    setHasChanges(true)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!contentData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Failed to load content</h2>
          <button 
            onClick={loadContent}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  const currentPage = contentData.pages.find(page => page.id === selectedPage)
  const currentSection = currentPage?.sections.find(section => section.id === selectedSection)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Content Management</h1>
              <p className="mt-2 text-gray-600">
                Manage all website content including hero sections, pages, and components
              </p>
            </div>
            <div className="flex items-center space-x-4">
              {hasChanges && (
                <span className="text-amber-600 text-sm font-medium">
                  Unsaved changes
                </span>
              )}
              <button
                onClick={saveContent}
                disabled={saving || !hasChanges}
                className={`flex items-center space-x-2 px-6 py-2 rounded-lg font-medium ${
                  hasChanges && !saving
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                <CheckIcon className="h-5 w-5" />
                <span>{saving ? 'Saving...' : 'Save Changes'}</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar - Page Navigation */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Pages</h2>
              <nav className="space-y-2">
                {contentData.pages.map(page => (
                  <button
                    key={page.id}
                    onClick={() => {
                      setSelectedPage(page.id)
                      setSelectedSection(null)
                    }}
                    className={`w-full text-left px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                      selectedPage === page.id
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <DocumentTextIcon className="h-4 w-4" />
                      <span>{page.name}</span>
                      <span className="text-xs text-gray-400">
                        ({page.sections.length})
                      </span>
                    </div>
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {currentPage && (
              <div className="space-y-6">
                {/* Page Header */}
                <div className="bg-white rounded-lg shadow-sm border p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-2">
                    {currentPage.name} Page
                  </h2>
                  <p className="text-gray-600">{currentPage.metaDescription}</p>
                </div>

                {/* Sections */}
                <div className="space-y-4">
                  {currentPage.sections.map(section => (
                    <SectionCard
                      key={section.id}
                      section={section}
                      pageId={currentPage.id}
                      isSelected={selectedSection === section.id}
                      onSelect={() => setSelectedSection(
                        selectedSection === section.id ? null : section.id
                      )}
                      onUpdate={(updates) => updateSection(currentPage.id, section.id, updates)}
                      onAddSlide={() => addSlide(currentPage.id, section.id)}
                      onRemoveSlide={(slideIndex) => removeSlide(currentPage.id, section.id, slideIndex)}
                      onUpdateSlide={(slideIndex, updates) => updateSlide(currentPage.id, section.id, slideIndex, updates)}
                      onMoveSlide={(slideIndex, direction) => moveSlide(currentPage.id, section.id, slideIndex, direction)}
                      onPreviewSlide={setPreviewSlide}
                      onPreviewCarousel={(section) => {
                        setPreviewSection(section)
                        setShowCarouselPreview(true)
                      }}
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Slide Preview Modal */}
      {previewSlide && (
        <SlidePreviewModal
          slide={previewSlide}
          onClose={() => setPreviewSlide(null)}
        />
      )}

      {/* Carousel Preview Modal */}
      {showCarouselPreview && previewSection && previewSection.slides && (
        <HeroCarouselPreview
          slides={previewSection.slides}
          isOpen={showCarouselPreview}
          onClose={() => {
            setShowCarouselPreview(false)
            setPreviewSection(null)
          }}
          autoplay={previewSection.fields?.autoplay !== false}
          speed={previewSection.fields?.slideshowSpeed || 5000}
          showDots={previewSection.fields?.showDots !== false}
          showArrows={previewSection.fields?.showArrows !== false}
        />
      )}
    </div>
  )
}

// Section Card Component
interface SectionCardProps {
  section: ContentSection
  pageId: string
  isSelected: boolean
  onSelect: () => void
  onUpdate: (updates: Partial<ContentSection>) => void
  onAddSlide: () => void
  onRemoveSlide: (slideIndex: number) => void
  onUpdateSlide: (slideIndex: number, updates: Partial<ContentSlide>) => void
  onMoveSlide: (slideIndex: number, direction: 'up' | 'down') => void
  onPreviewSlide: (slide: ContentSlide) => void
  onPreviewCarousel: (section: ContentSection) => void
}

function SectionCard({
  section,
  pageId,
  isSelected,
  onSelect,
  onUpdate,
  onAddSlide,
  onRemoveSlide,
  onUpdateSlide,
  onMoveSlide,
  onPreviewSlide,
  onPreviewCarousel
}: SectionCardProps) {
  const getSectionIcon = (type: string) => {
    switch (type) {
      case 'hero': return PhotoIcon
      case 'about': return DocumentTextIcon
      case 'services': return Cog6ToothIcon
      case 'projects': return BriefcaseIcon
      case 'technologies': return CpuChipIcon
      case 'team': return UserGroupIcon
      case 'blog': return PencilSquareIcon
      case 'contact': return PhoneIcon
      case 'testimonials': return ChatBubbleLeftRightIcon
      case 'legal': return DocumentTextIcon
      default: return DocumentTextIcon
    }
  }

  const SectionIcon = getSectionIcon(section.type)

  return (
    <div className="bg-white rounded-lg shadow-sm border">
      {/* Section Header */}
      <div 
        className="p-6 cursor-pointer hover:bg-gray-50 transition-colors"
        onClick={onSelect}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <SectionIcon className="h-6 w-6 text-gray-400" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">{section.title}</h3>
              <p className="text-sm text-gray-500 capitalize">
                {section.type} Section
                {section.slides && section.slides.length > 0 && (
                  <span className="ml-2">• {section.slides.length} slides</span>
                )}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
              section.isActive 
                ? 'bg-green-100 text-green-800' 
                : 'bg-gray-100 text-gray-800'
            }`}>
              {section.isActive ? 'Active' : 'Inactive'}
            </span>
            <button className="text-gray-400 hover:text-gray-600">
              {isSelected ? (
                <XMarkIcon className="h-5 w-5" />
              ) : (
                <EyeIcon className="h-5 w-5" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Expanded Section Content */}
      {isSelected && (
        <div className="border-t bg-gray-50 p-6">
          <SectionEditor
            section={section}
            onUpdate={onUpdate}
            onAddSlide={onAddSlide}
            onRemoveSlide={onRemoveSlide}
            onUpdateSlide={onUpdateSlide}
            onMoveSlide={onMoveSlide}
            onPreviewSlide={onPreviewSlide}
            onPreviewCarousel={onPreviewCarousel}
          />
        </div>
      )}
    </div>
  )
}

// Section Editor Component (will be continued in next file)
interface SectionEditorProps {
  section: ContentSection
  onUpdate: (updates: Partial<ContentSection>) => void
  onAddSlide: () => void
  onRemoveSlide: (slideIndex: number) => void
  onUpdateSlide: (slideIndex: number, updates: Partial<ContentSlide>) => void
  onMoveSlide: (slideIndex: number, direction: 'up' | 'down') => void
  onPreviewSlide: (slide: ContentSlide) => void
  onPreviewCarousel: (section: ContentSection) => void
}

function SectionEditor({
  section,
  onUpdate,
  onAddSlide,
  onRemoveSlide,
  onUpdateSlide,
  onMoveSlide,
  onPreviewSlide,
  onPreviewCarousel
}: SectionEditorProps) {
  return (
    <div className="space-y-6">
      {/* Basic Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Section Title
          </label>
          <input
            type="text"
            value={section.title}
            onChange={(e) => onUpdate({ title: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Subtitle
          </label>
          <input
            type="text"
            value={section.subtitle || ''}
            onChange={(e) => onUpdate({ subtitle: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Content */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Content
        </label>
        <textarea
          value={section.content || ''}
          onChange={(e) => onUpdate({ content: e.target.value })}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      {/* Hero Slides (if hero section) */}
      {section.type === 'hero' && (
        <div>
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-semibold text-gray-900">Hero Slides</h4>
            <div className="flex items-center space-x-2">
              {section.slides && section.slides.length > 0 && (
                <button
                  onClick={() => onPreviewCarousel(section)}
                  className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                >
                  <EyeIcon className="h-4 w-4" />
                  <span>Preview Carousel</span>
                </button>
              )}
              <button
                onClick={onAddSlide}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                <PlusIcon className="h-4 w-4" />
                <span>Add Slide</span>
              </button>
            </div>
          </div>
          
          {section.slides && section.slides.length > 0 ? (
            <div className="space-y-4">
              {section.slides.map((slide, index) => (
                <SlideEditor
                  key={index}
                  slide={slide}
                  index={index}
                  totalSlides={section.slides!.length}
                  onUpdate={(updates) => onUpdateSlide(index, updates)}
                  onRemove={() => onRemoveSlide(index)}
                  onMove={(direction) => onMoveSlide(index, direction)}
                  onPreview={() => onPreviewSlide(slide)}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No slides yet. Add your first slide to get started.
            </div>
          )}
        </div>
      )}

      {/* Status Toggle */}
      <div className="flex items-center space-x-3">
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={section.isActive}
            onChange={(e) => onUpdate({ isActive: e.target.checked })}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span className="ml-2 text-sm font-medium text-gray-700">
            Section Active
          </span>
        </label>
      </div>
    </div>
  )
}

// Slide Editor Component (will be continued in next file)
interface SlideEditorProps {
  slide: ContentSlide
  index: number
  totalSlides: number
  onUpdate: (updates: Partial<ContentSlide>) => void
  onRemove: () => void
  onMove: (direction: 'up' | 'down') => void
  onPreview: () => void
}

function SlideEditor({ slide, index, totalSlides, onUpdate, onRemove, onMove, onPreview }: SlideEditorProps) {
  return (
    <div className="bg-white border rounded-lg p-4">
      <div className="flex items-center justify-between mb-4">
        <h5 className="font-medium text-gray-900">Slide {index + 1}</h5>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => onMove('up')}
            disabled={index === 0}
            className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
          >
            <ArrowUpIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => onMove('down')}
            disabled={index === totalSlides - 1}
            className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
          >
            <ArrowDownIcon className="h-4 w-4" />
          </button>
          <button
            onClick={onPreview}
            className="p-1 text-blue-600 hover:text-blue-700"
          >
            <EyeIcon className="h-4 w-4" />
          </button>
          <button
            onClick={onRemove}
            className="p-1 text-red-600 hover:text-red-700"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Title</label>
          <input
            type="text"
            value={slide.title}
            onChange={(e) => onUpdate({ title: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Subtitle</label>
          <input
            type="text"
            value={slide.subtitle}
            onChange={(e) => onUpdate({ subtitle: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Button Text</label>
          <input
            type="text"
            value={slide.buttonText}
            onChange={(e) => onUpdate({ buttonText: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Button URL</label>
          <input
            type="text"
            value={slide.buttonUrl}
            onChange={(e) => onUpdate({ buttonUrl: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-1">Image URL</label>
          <input
            type="text"
            value={slide.imageUrl}
            onChange={(e) => onUpdate({ imageUrl: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      <div className="mt-4 flex items-center">
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={slide.isActive}
            onChange={(e) => onUpdate({ isActive: e.target.checked })}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span className="ml-2 text-sm text-gray-700">Active</span>
        </label>
      </div>
    </div>
  )
}

// Slide Preview Modal Component
interface SlidePreviewModalProps {
  slide: ContentSlide
  onClose: () => void
}

function SlidePreviewModal({ slide, onClose }: SlidePreviewModalProps) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Slide Preview</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
          
          <div className="relative bg-gray-900 rounded-lg overflow-hidden aspect-video">
            {slide.imageUrl && (
              <img
                src={slide.imageUrl}
                alt={slide.title}
                className="w-full h-full object-cover"
              />
            )}
            <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
              <div className="text-center text-white max-w-2xl px-6">
                <h1 className="text-4xl font-bold mb-4">{slide.title}</h1>
                <p className="text-xl mb-8">{slide.subtitle}</p>
                {slide.buttonText && (
                  <button className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                    {slide.buttonText}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
