'use client'

import React, { useEffect, useRef } from 'react'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Navigation, Pagination, Autoplay, EffectFade } from 'swiper/modules'
import type { Swiper as SwiperType } from 'swiper'

// Import Swiper styles
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import 'swiper/css/effect-fade'

import type { ContentSlide } from '@/app/api/content/route'

interface HeroCarouselProps {
  slides: ContentSlide[]
  autoplay?: boolean
  speed?: number
  showDots?: boolean
  showArrows?: boolean
  className?: string
  onSlideChange?: (index: number) => void
}

export default function HeroCarousel({
  slides,
  autoplay = true,
  speed = 5000,
  showDots = true,
  showArrows = true,
  className = '',
  onSlideChange
}: HeroCarouselProps) {
  const swiperRef = useRef<SwiperType>()

  // Filter active slides
  const activeSlides = slides.filter(slide => slide.isActive)

  if (activeSlides.length === 0) {
    return (
      <div className={`relative bg-gray-900 rounded-lg overflow-hidden aspect-video flex items-center justify-center ${className}`}>
        <div className="text-center text-white">
          <h2 className="text-2xl font-bold mb-2">No Active Slides</h2>
          <p className="text-gray-300">Add some slides to see the carousel preview</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`relative rounded-lg overflow-hidden ${className}`}>
      <Swiper
        modules={[Navigation, Pagination, Autoplay, EffectFade]}
        spaceBetween={0}
        slidesPerView={1}
        navigation={showArrows}
        pagination={showDots ? { clickable: true } : false}
        autoplay={autoplay ? {
          delay: speed,
          disableOnInteraction: false,
          pauseOnMouseEnter: true
        } : false}
        effect="fade"
        fadeEffect={{ crossFade: true }}
        loop={activeSlides.length > 1}
        onSwiper={(swiper) => {
          swiperRef.current = swiper
        }}
        onSlideChange={(swiper) => {
          onSlideChange?.(swiper.activeIndex)
        }}
        className="hero-carousel"
      >
        {activeSlides.map((slide, index) => (
          <SwiperSlide key={slide.id || index}>
            <HeroSlide slide={slide} />
          </SwiperSlide>
        ))}
      </Swiper>

      {/* Custom Navigation Buttons */}
      {showArrows && activeSlides.length > 1 && (
        <>
          <button
            onClick={() => swiperRef.current?.slidePrev()}
            className="absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full backdrop-blur-sm transition-all duration-200"
            aria-label="Previous slide"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <button
            onClick={() => swiperRef.current?.slideNext()}
            className="absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full backdrop-blur-sm transition-all duration-200"
            aria-label="Next slide"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </>
      )}

      {/* Custom Pagination Dots */}
      {showDots && activeSlides.length > 1 && (
        <div className="absolute bottom-6 left-1/2 -translate-x-1/2 z-10 flex space-x-2">
          {activeSlides.map((_, index) => (
            <button
              key={index}
              onClick={() => swiperRef.current?.slideTo(index)}
              className="w-3 h-3 rounded-full bg-white/50 hover:bg-white/80 transition-all duration-200"
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  )
}

// Individual Hero Slide Component
interface HeroSlideProps {
  slide: ContentSlide
}

function HeroSlide({ slide }: HeroSlideProps) {
  return (
    <div className="relative aspect-video bg-gray-900">
      {/* Background Image */}
      {slide.imageUrl && (
        <div className="absolute inset-0">
          <img
            src={slide.imageUrl}
            alt={slide.title}
            className="w-full h-full object-cover"
            onError={(e) => {
              // Fallback to gradient background if image fails to load
              const target = e.target as HTMLImageElement
              target.style.display = 'none'
            }}
          />
          {/* Overlay */}
          <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/40 to-black/60" />
        </div>
      )}

      {/* Fallback gradient background */}
      {!slide.imageUrl && (
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900" />
      )}

      {/* Content */}
      <div className="relative h-full flex items-center justify-center">
        <div className="text-center text-white max-w-4xl px-6 sm:px-8 lg:px-12">
          {/* Title */}
          <h1 className="text-3xl sm:text-4xl lg:text-6xl font-bold mb-4 sm:mb-6 leading-tight">
            {slide.title}
          </h1>

          {/* Subtitle */}
          {slide.subtitle && (
            <p className="text-lg sm:text-xl lg:text-2xl mb-6 sm:mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              {slide.subtitle}
            </p>
          )}

          {/* Button */}
          {slide.buttonText && (
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <button
                onClick={() => {
                  if (slide.buttonUrl && slide.buttonUrl !== '#') {
                    window.open(slide.buttonUrl, '_blank', 'noopener,noreferrer')
                  }
                }}
                className="inline-flex items-center px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
              >
                {slide.buttonText}
                <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Slide Indicator */}
      <div className="absolute top-4 right-4 bg-black/30 text-white px-3 py-1 rounded-full text-sm backdrop-blur-sm">
        {slide.isActive ? 'Active' : 'Inactive'}
      </div>
    </div>
  )
}

// Carousel Controls Component for Admin
interface CarouselControlsProps {
  isPlaying: boolean
  onPlayPause: () => void
  currentSlide: number
  totalSlides: number
  onSlideChange: (index: number) => void
}

export function CarouselControls({
  isPlaying,
  onPlayPause,
  currentSlide,
  totalSlides,
  onSlideChange
}: CarouselControlsProps) {
  return (
    <div className="flex items-center justify-between bg-white border rounded-lg p-4 mt-4">
      <div className="flex items-center space-x-4">
        <button
          onClick={onPlayPause}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          {isPlaying ? (
            <>
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z" />
              </svg>
              <span>Pause</span>
            </>
          ) : (
            <>
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M8 5v14l11-7z" />
              </svg>
              <span>Play</span>
            </>
          )}
        </button>

        <div className="text-sm text-gray-600">
          Slide {currentSlide + 1} of {totalSlides}
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <button
          onClick={() => onSlideChange(Math.max(0, currentSlide - 1))}
          disabled={currentSlide === 0}
          className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>

        <div className="flex space-x-1">
          {Array.from({ length: totalSlides }, (_, index) => (
            <button
              key={index}
              onClick={() => onSlideChange(index)}
              className={`w-2 h-2 rounded-full transition-colors ${
                index === currentSlide ? 'bg-blue-600' : 'bg-gray-300'
              }`}
            />
          ))}
        </div>

        <button
          onClick={() => onSlideChange(Math.min(totalSlides - 1, currentSlide + 1))}
          disabled={currentSlide === totalSlides - 1}
          className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    </div>
  )
}

// Preview Modal with Carousel
interface HeroCarouselPreviewProps {
  slides: ContentSlide[]
  isOpen: boolean
  onClose: () => void
  autoplay?: boolean
  speed?: number
  showDots?: boolean
  showArrows?: boolean
}

export function HeroCarouselPreview({
  slides,
  isOpen,
  onClose,
  autoplay = true,
  speed = 5000,
  showDots = true,
  showArrows = true
}: HeroCarouselPreviewProps) {
  const [currentSlide, setCurrentSlide] = React.useState(0)
  const [isPlaying, setIsPlaying] = React.useState(autoplay)

  if (!isOpen) return null

  const activeSlides = slides.filter(slide => slide.isActive)

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-gray-900">Hero Carousel Preview</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <HeroCarousel
            slides={activeSlides}
            autoplay={isPlaying}
            speed={speed}
            showDots={showDots}
            showArrows={showArrows}
            onSlideChange={setCurrentSlide}
            className="mb-4"
          />

          <CarouselControls
            isPlaying={isPlaying}
            onPlayPause={() => setIsPlaying(!isPlaying)}
            currentSlide={currentSlide}
            totalSlides={activeSlides.length}
            onSlideChange={setCurrentSlide}
          />
        </div>
      </div>
    </div>
  )
}
