'use client'

import { useState } from 'react'
import { useAdminData } from '@/lib/hooks/use-admin-data'
import { 
  PlusIcon, 
  PencilIcon, 
  TrashIcon, 
  EyeIcon,
  PhotoIcon,
  LinkIcon
} from '@heroicons/react/24/outline'

interface HeroSection {
  id: number
  title: string
  metaDescription?: string
  metaKeywords?: string
  pageName: string
  mainTitle: string
  mainSubtitle: string
  mainDescription?: string
  primaryButtonText?: string
  primaryButtonUrl?: string
  secondaryButtonText?: string
  secondaryButtonUrl?: string
  enableSlideshow: boolean
  slideshowSpeed: number
  autoplay: boolean
  showDots: boolean
  showArrows: boolean
  enableFloatingElements: boolean
  floatingElementsConfig?: string
  isActive: boolean
  modifiedBy?: string
  createdAt: string
  updatedAt: string
  slides: HeroSlide[]
}

interface HeroSlide {
  id: number
  heroSectionId: number
  content: string
  mediaType: 'image' | 'video'
  imageUrl?: string
  videoUrl?: string
  mediaAlt?: string
  videoAutoplay: boolean
  videoMuted: boolean
  videoLoop: boolean
  videoControls: boolean
  buttonText?: string
  buttonUrl?: string
  displayOrder: number
  isActive: boolean
  animationType: 'fade' | 'slide' | 'zoom'
  duration: number
  createdAt: string
  updatedAt: string
}

interface HeroSectionFormData {
  title: string
  metaDescription?: string
  metaKeywords?: string
  pageName: string
  mainTitle: string
  mainSubtitle: string
  mainDescription?: string
  primaryButtonText?: string
  primaryButtonUrl?: string
  secondaryButtonText?: string
  secondaryButtonUrl?: string
  enableSlideshow: boolean
  slideshowSpeed: number
  autoplay: boolean
  showDots: boolean
  showArrows: boolean
  enableFloatingElements: boolean
  floatingElementsConfig?: string
  isActive: boolean
}

export default function HeroSectionsManager() {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [editingSection, setEditingSection] = useState<HeroSection | null>(null)
  const [formData, setFormData] = useState<HeroSectionFormData>({
    title: '',
    metaDescription: '',
    metaKeywords: '',
    pageName: 'Home',
    mainTitle: '',
    mainSubtitle: '',
    mainDescription: '',
    primaryButtonText: '',
    primaryButtonUrl: '',
    secondaryButtonText: '',
    secondaryButtonUrl: '',
    enableSlideshow: true,
    slideshowSpeed: 5000,
    autoplay: true,
    showDots: true,
    showArrows: true,
    enableFloatingElements: true,
    floatingElementsConfig: '',
    isActive: true
  })

  const {
    data: heroSections,
    loading,
    error,
    pagination,
    refetch,
    create,
    update,
    remove
  } = useAdminData<HeroSection>('hero-sections')

  const handleCreate = async (e: React.FormEvent) => {
    e.preventDefault()
    const result = await create(formData)
    if (result) {
      setIsCreateModalOpen(false)
      setFormData({
        title: '',
        metaDescription: '',
        metaKeywords: '',
        pageName: 'Home',
        mainTitle: '',
        mainSubtitle: '',
        mainDescription: '',
        primaryButtonText: '',
        primaryButtonUrl: '',
        secondaryButtonText: '',
        secondaryButtonUrl: '',
        enableSlideshow: true,
        slideshowSpeed: 5000,
        autoplay: true,
        showDots: true,
        showArrows: true,
        enableFloatingElements: true,
        floatingElementsConfig: '',
        isActive: true
      })
    }
  }

  const handleUpdate = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!editingSection) return
    
    const result = await update(editingSection.id, formData)
    if (result) {
      setEditingSection(null)
      setFormData({
        title: '',
        metaDescription: '',
        metaKeywords: '',
        pageName: 'Home',
        mainTitle: '',
        mainSubtitle: '',
        mainDescription: '',
        primaryButtonText: '',
        primaryButtonUrl: '',
        secondaryButtonText: '',
        secondaryButtonUrl: '',
        enableSlideshow: true,
        slideshowSpeed: 5000,
        autoplay: true,
        showDots: true,
        showArrows: true,
        enableFloatingElements: true,
        floatingElementsConfig: '',
        isActive: true
      })
    }
  }

  const handleDelete = async (id: string) => {
    if (confirm('Are you sure you want to delete this hero section?')) {
      await remove(id)
    }
  }

  const openEditModal = (section: HeroSection) => {
    setEditingSection(section)
    setFormData({
      title: section.title,
      metaDescription: section.metaDescription || '',
      metaKeywords: section.metaKeywords || '',
      pageName: section.pageName,
      mainTitle: section.mainTitle,
      mainSubtitle: section.mainSubtitle,
      mainDescription: section.mainDescription || '',
      primaryButtonText: section.primaryButtonText || '',
      primaryButtonUrl: section.primaryButtonUrl || '',
      secondaryButtonText: section.secondaryButtonText || '',
      secondaryButtonUrl: section.secondaryButtonUrl || '',
      enableSlideshow: section.enableSlideshow,
      slideshowSpeed: section.slideshowSpeed,
      autoplay: section.autoplay,
      showDots: section.showDots,
      showArrows: section.showArrows,
      enableFloatingElements: section.enableFloatingElements,
      floatingElementsConfig: section.floatingElementsConfig || '',
      isActive: section.isActive
    })
  }

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="bg-white p-6 rounded-lg shadow">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <p className="text-red-700">{error}</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Hero Sections</h1>
          <p className="text-gray-600">Manage homepage hero sections and slides</p>
        </div>
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
        >
          <PlusIcon className="h-5 w-5" />
          <span>Add Hero Section</span>
        </button>
      </div>

      {/* Hero Sections List */}
      <div className="grid gap-6">
        {heroSections.map((section) => (
          <div key={section.id} className="bg-white rounded-lg shadow-md p-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{section.title}</h3>
                <p className="text-sm text-gray-500">Page: {section.pageName}</p>
                <p className="text-md text-gray-700 mt-1">{section.mainTitle}</p>
                <p className="text-sm text-gray-600">{section.mainSubtitle}</p>
                {section.mainDescription && (
                  <p className="text-gray-600 mt-1 text-sm">{section.mainDescription}</p>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <span className={`px-2 py-1 text-xs rounded-full ${
                  section.isActive 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {section.isActive ? 'Active' : 'Inactive'}
                </span>
                <div className="flex space-x-1">
                  <button
                    onClick={() => openEditModal(section)}
                    className="p-2 text-gray-400 hover:text-blue-600"
                  >
                    <PencilIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(section.id)}
                    className="p-2 text-gray-400 hover:text-red-600"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>

            {/* Additional Info */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">Slideshow:</span>
                <span className={`ml-1 ${section.enableSlideshow ? 'text-green-600' : 'text-gray-500'}`}>
                  {section.enableSlideshow ? 'Enabled' : 'Disabled'}
                </span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Autoplay:</span>
                <span className={`ml-1 ${section.autoplay ? 'text-green-600' : 'text-gray-500'}`}>
                  {section.autoplay ? 'On' : 'Off'}
                </span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Speed:</span>
                <span className="ml-1 text-gray-600">{section.slideshowSpeed}ms</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Slides:</span>
                <span className="ml-1 text-gray-600">{section.slides?.length || 0}</span>
              </div>
            </div>

            {/* Buttons */}
            {(section.primaryButtonText || section.secondaryButtonText) && (
              <div className="flex flex-wrap gap-2 mb-4">
                {section.primaryButtonText && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                    Primary: {section.primaryButtonText}
                  </span>
                )}
                {section.secondaryButtonText && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
                    Secondary: {section.secondaryButtonText}
                  </span>
                )}
              </div>
            )}

            {/* Slides */}
            {section.slides && section.slides.length > 0 && (
              <div className="border-t pt-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">
                  Slides ({section.slides.length})
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {section.slides.map((slide) => (
                    <div key={slide.id} className="border rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">{slide.content.substring(0, 30)}...</span>
                        <span className={`px-2 py-1 text-xs rounded ${
                          slide.isActive
                            ? 'bg-green-100 text-green-700'
                            : 'bg-gray-100 text-gray-600'
                        }`}>
                          {slide.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                      <div className="text-xs text-gray-600 mb-1">
                        Type: {slide.mediaType} | Animation: {slide.animationType}
                      </div>
                      <div className="flex items-center space-x-2 text-xs text-gray-500">
                        {slide.imageUrl && <PhotoIcon className="h-3 w-3" />}
                        {slide.buttonUrl && <LinkIcon className="h-3 w-3" />}
                        <span>Order: {slide.displayOrder}</span>
                        <span>Duration: {slide.duration}ms</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className="flex justify-center">
          <nav className="flex space-x-2">
            {[...Array(pagination.totalPages)].map((_, i) => (
              <button
                key={i}
                className={`px-3 py-2 rounded ${
                  pagination.page === i + 1
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {i + 1}
              </button>
            ))}
          </nav>
        </div>
      )}

      {/* Create/Edit Modal */}
      {(isCreateModalOpen || editingSection) && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <h2 className="text-lg font-semibold mb-4">
              {editingSection ? 'Edit Hero Section' : 'Create Hero Section'}
            </h2>
            <form onSubmit={editingSection ? handleUpdate : handleCreate}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Basic Information */}
                <div className="space-y-4">
                  <h3 className="text-md font-medium text-gray-900 border-b pb-2">Basic Information</h3>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Page Name</label>
                    <select
                      value={formData.pageName}
                      onChange={(e) => setFormData({ ...formData, pageName: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      required
                    >
                      <option value="Home">Home</option>
                      <option value="About">About</option>
                      <option value="Services">Services</option>
                      <option value="Contact">Contact</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Title</label>
                    <input
                      type="text"
                      value={formData.title}
                      onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Main Title</label>
                    <input
                      type="text"
                      value={formData.mainTitle}
                      onChange={(e) => setFormData({ ...formData, mainTitle: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Main Subtitle</label>
                    <input
                      type="text"
                      value={formData.mainSubtitle}
                      onChange={(e) => setFormData({ ...formData, mainSubtitle: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Main Description</label>
                    <textarea
                      value={formData.mainDescription}
                      onChange={(e) => setFormData({ ...formData, mainDescription: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      rows={3}
                    />
                  </div>
                </div>

                {/* SEO & Buttons */}
                <div className="space-y-4">
                  <h3 className="text-md font-medium text-gray-900 border-b pb-2">SEO & Buttons</h3>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Meta Description</label>
                    <textarea
                      value={formData.metaDescription}
                      onChange={(e) => setFormData({ ...formData, metaDescription: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      rows={2}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Meta Keywords</label>
                    <input
                      type="text"
                      value={formData.metaKeywords}
                      onChange={(e) => setFormData({ ...formData, metaKeywords: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      placeholder="keyword1, keyword2, keyword3"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Primary Button Text</label>
                    <input
                      type="text"
                      value={formData.primaryButtonText}
                      onChange={(e) => setFormData({ ...formData, primaryButtonText: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Primary Button URL</label>
                    <input
                      type="text"
                      value={formData.primaryButtonUrl}
                      onChange={(e) => setFormData({ ...formData, primaryButtonUrl: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Secondary Button Text</label>
                    <input
                      type="text"
                      value={formData.secondaryButtonText}
                      onChange={(e) => setFormData({ ...formData, secondaryButtonText: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Secondary Button URL</label>
                    <input
                      type="text"
                      value={formData.secondaryButtonUrl}
                      onChange={(e) => setFormData({ ...formData, secondaryButtonUrl: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    />
                  </div>
                </div>
              </div>

              {/* Slideshow Settings */}
              <div className="mt-6 space-y-4">
                <h3 className="text-md font-medium text-gray-900 border-b pb-2">Slideshow Settings</h3>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="enableSlideshow"
                      checked={formData.enableSlideshow}
                      onChange={(e) => setFormData({ ...formData, enableSlideshow: e.target.checked })}
                      className="mr-2"
                    />
                    <label htmlFor="enableSlideshow" className="text-sm font-medium text-gray-700">
                      Enable Slideshow
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="autoplay"
                      checked={formData.autoplay}
                      onChange={(e) => setFormData({ ...formData, autoplay: e.target.checked })}
                      className="mr-2"
                    />
                    <label htmlFor="autoplay" className="text-sm font-medium text-gray-700">
                      Autoplay
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="showDots"
                      checked={formData.showDots}
                      onChange={(e) => setFormData({ ...formData, showDots: e.target.checked })}
                      className="mr-2"
                    />
                    <label htmlFor="showDots" className="text-sm font-medium text-gray-700">
                      Show Dots
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="showArrows"
                      checked={formData.showArrows}
                      onChange={(e) => setFormData({ ...formData, showArrows: e.target.checked })}
                      className="mr-2"
                    />
                    <label htmlFor="showArrows" className="text-sm font-medium text-gray-700">
                      Show Arrows
                    </label>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Slideshow Speed (ms)</label>
                    <input
                      type="number"
                      min="1000"
                      max="10000"
                      step="500"
                      value={formData.slideshowSpeed}
                      onChange={(e) => setFormData({ ...formData, slideshowSpeed: parseInt(e.target.value) })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    />
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="enableFloatingElements"
                      checked={formData.enableFloatingElements}
                      onChange={(e) => setFormData({ ...formData, enableFloatingElements: e.target.checked })}
                      className="mr-2"
                    />
                    <label htmlFor="enableFloatingElements" className="text-sm font-medium text-gray-700">
                      Enable Floating Elements
                    </label>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Floating Elements Config (JSON)</label>
                  <textarea
                    value={formData.floatingElementsConfig}
                    onChange={(e) => setFormData({ ...formData, floatingElementsConfig: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    rows={2}
                    placeholder='{"elements": []}'
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isActive"
                    checked={formData.isActive}
                    onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                    className="mr-2"
                  />
                  <label htmlFor="isActive" className="text-sm font-medium text-gray-700">
                    Active
                  </label>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => {
                    setIsCreateModalOpen(false)
                    setEditingSection(null)
                  }}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  {editingSection ? 'Update' : 'Create'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}
