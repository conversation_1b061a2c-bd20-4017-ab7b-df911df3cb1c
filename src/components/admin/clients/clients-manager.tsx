'use client'

// Updated with Services page styling - v2.0
import React, { useState, useEffect, useRef } from 'react'
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  RectangleStackIcon,
  AdjustmentsHorizontalIcon,
  EyeIcon,
  EyeSlashIcon,
  ChevronDownIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  PowerIcon,
  BuildingOfficeIcon,
  DocumentIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline'
import { ClientModal } from './client-modal'
import { ClientAvatar } from './client-avatar'
import { CrudConfig } from '../crud/types'
import { motion, AnimatePresence } from 'framer-motion'

interface Client {
  id: string | number
  companyName: string
  contactName: string
  contactEmail: string
  contactPhone?: string
  website?: string
  industry: string
  companySize: string
  address?: string
  city?: string
  state?: string
  country?: string
  zipCode?: string
  isActive: boolean
  notes?: string
  createdAt: string
  updatedAt: string
  [key: string]: any
}

interface ViewSettings {
  mode: 'list' | 'grid' | 'cards'
  density: 'compact' | 'comfortable'
  visibleColumns: string[]
}

interface ClientsManagerProps {
  config: CrudConfig<Client>
}

export function ClientsManager({ config }: ClientsManagerProps) {
  // State management
  const [clients, setClients] = useState<Client[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  const [selectedClients, setSelectedClients] = useState<string[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalCount, setTotalCount] = useState(0)
  const [sortField, setSortField] = useState(config.defaultSort?.field || 'updatedAt')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>(config.defaultSort?.direction || 'desc')
  const [filters, setFilters] = useState<Record<string, string>>({})
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  // Modal states
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingClient, setEditingClient] = useState<Client | null>(null)

  // View settings
  const [viewSettings, setViewSettings] = useState<ViewSettings>({
    mode: config.defaultViewSettings?.mode || 'list',
    density: config.defaultViewSettings?.density || 'comfortable',
    visibleColumns: config.defaultViewSettings?.visibleColumns || config.columns.map(col => col.key as string)
  })

  // UI states
  const [showFilters, setShowFilters] = useState(false)
  const [showColumnVisibility, setShowColumnVisibility] = useState(false)
  const [showDensityMenu, setShowDensityMenu] = useState(false)
  const [showSortMenu, setShowSortMenu] = useState(false)
  const [showBulkActions, setShowBulkActions] = useState(false)

  const searchInputRef = useRef<HTMLInputElement>(null)

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, 300)

    return () => clearTimeout(timer)
  }, [searchTerm])

  // Fetch clients when dependencies change
  useEffect(() => {
    fetchClients()
  }, [debouncedSearchTerm, currentPage, sortField, sortDirection, filters])

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (!target.closest('.dropdown-container')) {
        setShowDensityMenu(false)
        setShowColumnVisibility(false)
        setShowSortMenu(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const fetchClients = async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: (config.pageSize || 10).toString(),
        sortBy: sortField,
        sortOrder: sortDirection,
        ...(debouncedSearchTerm && { search: debouncedSearchTerm })
      })

      // Add filters as JSON string if any filters are set
      const activeFilters = Object.fromEntries(
        Object.entries(filters).filter(([_, value]) => value && value !== '')
      )
      if (Object.keys(activeFilters).length > 0) {
        params.append('filter', JSON.stringify(activeFilters))
      }

      const response = await fetch(`/api/admin/${config.endpoint}?${params}`)

      if (!response.ok) {
        throw new Error(`Failed to fetch clients: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        setClients(data.data || [])
        setTotalPages(data.pagination?.totalPages || 1)
        setTotalCount(data.pagination?.total || 0)
      } else {
        throw new Error(data.error || 'Failed to fetch clients')
      }
    } catch (err) {
      console.error('Error fetching clients:', err)
      setError(err instanceof Error ? err.message : 'An error occurred')
      setClients([])
    } finally {
      setLoading(false)
    }
  }

  const handleCreate = async (data: any) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create client')
      }

      const result = await response.json()
      
      if (result.success) {
        setIsCreateModalOpen(false)
        await fetchClients()
        alert('Client created successfully!')
      } else {
        throw new Error(result.error || 'Failed to create client')
      }
    } catch (error) {
      console.error('Error creating client:', error)
      alert(error instanceof Error ? error.message : 'Failed to create client')
      throw error
    }
  }

  const handleUpdate = async (id: string, data: any) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update client')
      }

      const result = await response.json()
      
      if (result.success) {
        setIsEditModalOpen(false)
        setEditingClient(null)
        await fetchClients()
        alert('Client updated successfully!')
      } else {
        throw new Error(result.error || 'Failed to update client')
      }
    } catch (error) {
      console.error('Error updating client:', error)
      alert(error instanceof Error ? error.message : 'Failed to update client')
      throw error
    }
  }

  const handleDelete = async (id: string) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete client')
      }

      const result = await response.json()
      
      if (result.success) {
        await fetchClients()
        // Show success notification
      } else {
        throw new Error(result.error || 'Failed to delete client')
      }
    } catch (error) {
      console.error('Error deleting client:', error)
      throw error
    }
  }

  const handleToggleStatus = async (id: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update client status')
      }

      const result = await response.json()
      
      if (result.success) {
        await fetchClients()
        // Show success notification
      } else {
        throw new Error(result.error || 'Failed to update client status')
      }
    } catch (error) {
      console.error('Error updating client status:', error)
      throw error
    }
  }

  const handleBulkAction = async (action: string, clientIds: string[]) => {
    try {
      let endpoint = `/api/admin/${config.endpoint}`
      let method = 'PUT'
      let body: any = { ids: clientIds }

      switch (action) {
        case 'activate':
          body.data = { isActive: true }
          break
        case 'deactivate':
          body.data = { isActive: false }
          break
        case 'delete':
          method = 'DELETE'
          body = { ids: clientIds }
          break
        default:
          throw new Error(`Unknown bulk action: ${action}`)
      }

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Failed to ${action} clients`)
      }

      const result = await response.json()
      
      if (result.success) {
        setSelectedClients([])
        await fetchClients()
        // Show success notification
      } else {
        throw new Error(result.error || `Failed to ${action} clients`)
      }
    } catch (error) {
      console.error(`Error performing bulk ${action}:`, error)
      throw error
    }
  }

  // Handle individual actions
  const handleAction = async (action: string, item: any) => {
    const actionKey = `${action}-${String(item.id)}`

    try {
      setActionLoading(actionKey)

      switch (action) {
        case 'view':
          // Open client details in modal or navigate to detail page
          console.log('View client:', item)
          break

        case 'edit':
          setEditingClient(item)
          setIsEditModalOpen(true)
          break

        case 'toggle-status':
          await handleToggleStatus(String(item.id), !item.isActive)
          break

        case 'delete':
          if (window.confirm('Are you sure you want to delete this client? This action cannot be undone.')) {
            await handleDelete(String(item.id))
          }
          break

        default:
          console.warn(`Unknown action: ${action}`)
      }
    } finally {
      setActionLoading(null)
    }
  }

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
    setCurrentPage(1)
  }

  // Get density-based classes
  const getDensityClasses = () => {
    return viewSettings.density === 'compact'
      ? 'px-4 py-2'
      : 'px-6 py-4'
  }

  const getHeaderDensityClasses = () => {
    return viewSettings.density === 'compact'
      ? 'px-4 py-2'
      : 'px-6 py-3'
  }

  const handleFilterChange = (filterKey: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [filterKey]: value
    }))
    setCurrentPage(1)
  }

  const handleSelectAll = () => {
    if (selectedClients.length === clients.length) {
      setSelectedClients([])
    } else {
      setSelectedClients(clients.map(client => String(client.id)))
    }
  }

  const handleSelectClient = (clientId: string) => {
    setSelectedClients(prev =>
      prev.includes(clientId)
        ? prev.filter(id => id !== clientId)
        : [...prev, clientId]
    )
  }

  const clearSearch = () => {
    setSearchTerm('')
    if (searchInputRef.current) {
      searchInputRef.current.focus()
    }
  }

  const renderActionButton = (action: any, item: any) => {
    const isLoading = actionLoading === `${action.action}-${String(item.id)}`
    const IconComponent = getIconComponent(action.icon)

    return (
      <button
        key={action.action}
        onClick={() => handleAction(action.action, item)}
        disabled={isLoading || action.disabled}
        className={`p-1.5 rounded-md transition-colors focus:outline-none focus:ring-2 ${getActionButtonClasses(action.variant)} ${
          isLoading || action.disabled ? 'opacity-50 cursor-not-allowed' : ''
        }`}
        title={action.tooltip || action.label}
      >
        {isLoading ? (
          <div className="w-4 h-4 border border-current border-t-transparent rounded-full animate-spin" />
        ) : (
          IconComponent && <IconComponent className="w-4 h-4" />
        )}
      </button>
    )
  }

  const getIconComponent = (iconName: string) => {
    const iconMap: Record<string, any> = {
      'EyeIcon': EyeIcon,
      'PencilIcon': PencilIcon,
      'TrashIcon': TrashIcon,
      'PowerIcon': PowerIcon,
      'BuildingOfficeIcon': BuildingOfficeIcon,
      'DocumentIcon': DocumentIcon
    }
    return iconMap[iconName]
  }

  const getActionButtonClasses = (variant?: string) => {
    switch (variant) {
      case 'primary':
        return 'text-gray-400 hover:text-blue-600 hover:bg-blue-50 focus:ring-blue-500'
      case 'secondary':
        return 'text-gray-400 hover:text-gray-600 hover:bg-gray-50 focus:ring-gray-500'
      case 'danger':
        return 'text-gray-400 hover:text-red-600 hover:bg-red-50 focus:ring-red-500'
      case 'warning':
        return 'text-gray-400 hover:text-yellow-600 hover:bg-yellow-50 focus:ring-yellow-500'
      case 'success':
        return 'text-gray-400 hover:text-green-600 hover:bg-green-50 focus:ring-green-500'
      default:
        return 'text-gray-400 hover:text-gray-600 hover:bg-gray-50 focus:ring-gray-500'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const renderCellContent = (client: Client, column: any) => {
    const value = client[column.key as keyof Client]

    switch (column.renderType) {
      case 'email':
        return value ? (
          <a href={`mailto:${value}`} className="text-blue-600 hover:text-blue-800">
            {value}
          </a>
        ) : '-'

      case 'date':
        return value ? formatDate(value as string) : '-'

      case 'currency':
        return typeof value === 'number' ? formatCurrency(value) : '-'

      case 'boolean':
      case 'status':
        if (column.key === 'isActive') {
          return (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {value ? 'Active' : 'Inactive'}
            </span>
          )
        }
        return value ? 'Yes' : 'No'

      case 'company':
        return (
          <div className="flex items-center space-x-3">
            <ClientAvatar client={client} size="sm" />
            <div>
              <div className="text-sm font-medium text-gray-900">{value}</div>
              {client.website && (
                <div className="text-sm text-gray-500">{client.website}</div>
              )}
            </div>
          </div>
        )

      default:
        return value || '-'
    }
  }

  const renderTableView = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {config.enableBulkActions && (
                <th className={`w-12 ${getHeaderDensityClasses()} text-left`}>
                  <input
                    type="checkbox"
                    checked={selectedClients.length === clients.length && clients.length > 0}
                    onChange={handleSelectAll}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </th>
              )}
              {config.columns
                .filter(col => viewSettings.visibleColumns.includes(col.key as string))
                .map((column) => (
                  <th
                    key={column.key as string}
                    className={`${getHeaderDensityClasses()} text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${
                      column.sortable ? 'cursor-pointer hover:bg-gray-100' : ''
                    }`}
                    onClick={() => column.sortable && handleSort(column.key as string)}
                  >
                    <div className="flex items-center space-x-1">
                      <span>{column.label}</span>
                      {column.sortable && sortField === column.key && (
                        sortDirection === 'asc' ? (
                          <ArrowUpIcon className="h-3 w-3" />
                        ) : (
                          <ArrowDownIcon className="h-3 w-3" />
                        )
                      )}
                    </div>
                  </th>
                ))}
              {config.actions && config.actions.length > 0 && (
                <th className={`${getHeaderDensityClasses()} text-right text-xs font-medium text-gray-500 uppercase tracking-wider`}>
                  Actions
                </th>
              )}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {clients.length === 0 ? (
              <tr>
                <td colSpan={config.columns.filter(col => viewSettings.visibleColumns.includes(col.key as string)).length + (config.enableBulkActions ? 1 : 0) + (config.actions && config.actions.length > 0 ? 1 : 0)} className="px-6 py-12 text-center">
                  <div className="text-gray-500">
                    {debouncedSearchTerm ? 'No clients found matching your search.' : 'No clients found.'}
                  </div>
                </td>
              </tr>
            ) : (
              clients.map((client) => (
                <tr
                  key={client.id}
                  className={`hover:bg-gray-50 ${
                    selectedClients.includes(String(client.id)) ? 'bg-blue-50' : ''
                  }`}
                >
                  {config.enableBulkActions && (
                    <td className={`${getDensityClasses()} whitespace-nowrap`}>
                      <input
                        type="checkbox"
                        checked={selectedClients.includes(String(client.id))}
                        onChange={() => handleSelectClient(String(client.id))}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </td>
                  )}
                  {config.columns
                    .filter(col => viewSettings.visibleColumns.includes(col.key as string))
                    .map((column) => (
                      <td
                        key={column.key as string}
                        className={`${getDensityClasses()} whitespace-nowrap`}
                      >
                        {renderCellContent(client, column)}
                      </td>
                    ))}
                  {config.actions && config.actions.length > 0 && (
                    <td className={`${getDensityClasses()} whitespace-nowrap text-right text-sm font-medium`}>
                      <div className="flex items-center justify-end space-x-2">
                        {config.actions.map((action) => renderActionButton(action, client))}
                      </div>
                    </td>
                  )}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  )

  const renderGridView = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {clients.length === 0 ? (
          <div className="col-span-full text-center py-12 text-gray-500">
            {debouncedSearchTerm ? 'No clients found matching your search.' : 'No clients found.'}
          </div>
        ) : (
          clients.map((client) => (
          <motion.div
            key={client.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center space-x-3 mb-3">
              <ClientAvatar client={client} size="md" />
              <div className="flex-1 min-w-0">
                <h3 className="text-sm font-medium text-gray-900 truncate" title={client.companyName}>
                  {client.companyName}
                </h3>
                <p className="text-xs text-gray-500 truncate" title={client.contactName}>
                  {client.contactName}
                </p>
              </div>
              {config.enableBulkActions && (
                <input
                  type="checkbox"
                  checked={selectedClients.includes(String(client.id))}
                  onChange={() => handleSelectClient(String(client.id))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded flex-shrink-0"
                />
              )}
            </div>

            <div className="space-y-1 mb-3">
              <div className="flex items-center justify-between text-xs">
                <span className="text-gray-500">Email:</span>
                <span className="font-medium text-gray-900 truncate ml-2" title={client.contactEmail}>
                  {client.contactEmail || '-'}
                </span>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="text-gray-500">Website:</span>
                <span className="font-medium text-gray-900 truncate ml-2" title={client.website}>
                  {client.website || '-'}
                </span>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="text-gray-500">Status:</span>
                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium flex-shrink-0 ml-2 ${
                  client.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {client.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>
            </div>

            {config.actions && config.actions.length > 0 && (
              <div className="flex items-center space-x-1 pt-3 border-t border-gray-200">
                {config.actions.map((action) => renderActionButton(action, client))}
              </div>
            )}
          </motion.div>
        ))}
      </div>
    </div>
  )

  const renderCardsView = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="space-y-4">
        {clients.length === 0 ? (
          <div className="text-center py-12 text-gray-500">
            {debouncedSearchTerm ? 'No clients found matching your search.' : 'No clients found.'}
          </div>
        ) : (
          clients.map((client) => (
          <motion.div
            key={client.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3 flex-1 min-w-0">
                <ClientAvatar client={client} size="lg" />
                <div className="flex-1 min-w-0">
                  <h3 className="text-lg font-medium text-gray-900" title={client.companyName}>
                    {client.companyName}
                  </h3>
                  <p className="text-sm text-gray-500" title={client.contactName}>
                    {client.contactName}
                  </p>
                </div>
              </div>
              {config.enableBulkActions && (
                <input
                  type="checkbox"
                  checked={selectedClients.includes(String(client.id))}
                  onChange={() => handleSelectClient(String(client.id))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded flex-shrink-0 ml-3"
                />
              )}
            </div>

            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <span className="text-sm font-medium text-gray-500">Email</span>
                <p className="text-sm text-gray-900 truncate" title={client.contactEmail}>
                  {client.contactEmail || '-'}
                </p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Phone</span>
                <p className="text-sm text-gray-900" title={client.contactPhone}>
                  {client.contactPhone || '-'}
                </p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Website</span>
                <p className="text-sm text-gray-900 truncate" title={client.website}>
                  {client.website || '-'}
                </p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Status</span>
                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                  client.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {client.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>
            </div>

            {client.notes && (
              <div className="mb-4">
                <span className="text-sm font-medium text-gray-500">Notes</span>
                <p className="text-sm text-gray-900 mt-1 overflow-hidden" style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical'
                }} title={client.notes}>
                  {client.notes}
                </p>
              </div>
            )}

            {config.actions && config.actions.length > 0 && (
              <div className="flex items-center space-x-1 pt-4 border-t border-gray-200">
                {config.actions.map((action) => renderActionButton(action, client))}
              </div>
            )}
          </motion.div>
          ))
        )}
      </div>
    </div>
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center">
          <XMarkIcon className="w-5 h-5 text-red-400 mr-2" />
          <p className="text-red-800">{error}</p>
        </div>
        <button
          onClick={fetchClients}
          className="mt-4 bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors"
        >
          Try Again
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-6 mx-4 sm:mx-6 lg:mx-8 xl:mx-12 2xl:mx-16">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{config.title}</h1>
          {config.description && (
            <p className="text-gray-600">{config.description}</p>
          )}
        </div>
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
        >
          <span>+</span>
          <span>Add Client</span>
        </button>
      </div>

      {/* Comprehensive Search and Controls Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 space-y-4">
        {/* Search and Primary Controls */}
        <div className="flex items-center justify-between space-x-4">
          <div className="flex items-center space-x-4 flex-1">
            {/* Search Bar */}
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Search clients by company, contact, email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
              />
              {/* Search Loading Indicator */}
              {searchTerm !== debouncedSearchTerm && (
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent"></div>
                </div>
              )}
              {/* Clear Search Button */}
              {searchTerm && (
                <button
                  onClick={clearSearch}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              )}
            </div>

            {/* Filters Button */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-3 border rounded-lg transition-colors flex items-center space-x-2 ${
                showFilters
                  ? 'bg-blue-50 border-blue-300 text-blue-700'
                  : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
            >
              <FunnelIcon className="h-4 w-4" />
              <span>Filters</span>
            </button>
          </div>

          {/* View Controls */}
          <div className="flex items-center space-x-2 bg-gray-50 border border-gray-200 rounded-lg p-1">
            {/* View Mode Toggle */}
            {config.enableViewControls && (
              <div className="flex items-center bg-white rounded-md p-1">
                <button
                  onClick={() => setViewSettings(prev => ({ ...prev, mode: 'list' }))}
                  className={`p-2 rounded transition-colors ${
                    viewSettings.mode === 'list'
                      ? 'bg-blue-600 text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                  title="List view"
                >
                  <ListBulletIcon className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewSettings(prev => ({ ...prev, mode: 'grid' }))}
                  className={`p-2 rounded transition-colors ${
                    viewSettings.mode === 'grid'
                      ? 'bg-blue-600 text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                  title="Grid view"
                >
                  <Squares2X2Icon className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewSettings(prev => ({ ...prev, mode: 'cards' }))}
                  className={`p-2 rounded transition-colors ${
                    viewSettings.mode === 'cards'
                      ? 'bg-blue-600 text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                  title="Cards view"
                >
                  <RectangleStackIcon className="h-4 w-4" />
                </button>
              </div>
            )}

            {/* Display Density */}
            {config.enableDensityControls && (
              <div className="relative">
                <button
                  onClick={() => setShowDensityMenu(!showDensityMenu)}
                  className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                  title="Display density"
                >
                  <AdjustmentsHorizontalIcon className="h-4 w-4" />
                  <span>{viewSettings.density === 'compact' ? 'Compact' : 'Comfortable'}</span>
                  <ChevronDownIcon className="h-3 w-3" />
                </button>
                {showDensityMenu && (
                  <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[140px]">
                    <button
                      onClick={() => {
                        setViewSettings(prev => ({ ...prev, density: 'compact' }))
                        setShowDensityMenu(false)
                      }}
                      className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg transition-colors ${
                        viewSettings.density === 'compact' ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                      }`}
                    >
                      Compact
                    </button>
                    <button
                      onClick={() => {
                        setViewSettings(prev => ({ ...prev, density: 'comfortable' }))
                        setShowDensityMenu(false)
                      }}
                      className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg transition-colors ${
                        viewSettings.density === 'comfortable' ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                      }`}
                    >
                      Comfortable
                    </button>
                  </div>
                )}
              </div>
            )}

            {/* Column Visibility */}
            {config.enableColumnVisibility && viewSettings.mode === 'list' && (
              <div className="relative">
                <button
                  onClick={() => setShowColumnVisibility(!showColumnVisibility)}
                  className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                  title="Column visibility"
                >
                  <EyeIcon className="h-4 w-4" />
                  <span>Columns ({viewSettings.visibleColumns.length}/{config.columns.length})</span>
                  <ChevronDownIcon className="h-3 w-3" />
                </button>
                {showColumnVisibility && (
                  <div className="absolute top-full right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[200px] max-h-64 overflow-y-auto">
                    <div className="p-2">
                      <div className="flex items-center justify-between mb-2 pb-2 border-b border-gray-100">
                        <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                          Column Visibility
                        </span>
                        <button
                          onClick={() => {
                            setViewSettings(prev => ({
                              ...prev,
                              visibleColumns: config.defaultViewSettings?.visibleColumns || config.columns.filter(col => col.defaultVisible !== false).map(col => col.key as string)
                            }))
                            setShowColumnVisibility(false)
                          }}
                          className="text-xs text-blue-600 hover:text-blue-800"
                        >
                          Reset
                        </button>
                      </div>
                      <div className="space-y-1">
                        {config.columns.map((column) => (
                          <label
                            key={column.key as string}
                            className="flex items-center space-x-3 py-2 px-2 hover:bg-gray-50 rounded cursor-pointer transition-colors"
                          >
                            <input
                              type="checkbox"
                              checked={viewSettings.visibleColumns.includes(column.key as string)}
                              onChange={(e) => {
                                const columnKey = column.key as string
                                setViewSettings(prev => ({
                                  ...prev,
                                  visibleColumns: e.target.checked
                                    ? [...prev.visibleColumns, columnKey]
                                    : prev.visibleColumns.filter(col => col !== columnKey)
                                }))
                              }}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 h-4 w-4"
                            />
                            <span className="text-sm text-gray-700 flex-1">{column.label}</span>
                            {viewSettings.visibleColumns.includes(column.key as string) ? (
                              <EyeIcon className="h-4 w-4 text-green-500" />
                            ) : (
                              <EyeSlashIcon className="h-4 w-4 text-gray-400" />
                            )}
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Sort Controls */}
            <div className="relative">
              <button
                onClick={() => setShowSortMenu(!showSortMenu)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                title="Sort options"
              >
                {sortDirection === 'asc' ? (
                  <ArrowUpIcon className="h-4 w-4" />
                ) : (
                  <ArrowDownIcon className="h-4 w-4" />
                )}
                <span>Sort</span>
                <ChevronDownIcon className="h-3 w-3" />
              </button>
              {showSortMenu && (
                <div className="absolute top-full right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[180px]">
                  <div className="p-2">
                    <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2 pb-2 border-b border-gray-100">
                      Sort By
                    </div>
                    {config.columns.filter(col => col.sortable).map((column) => (
                      <button
                        key={column.key as string}
                        onClick={() => {
                          handleSort(column.key as string)
                          setShowSortMenu(false)
                        }}
                        className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded transition-colors flex items-center justify-between ${
                          sortField === column.key
                            ? 'bg-blue-50 text-blue-700'
                            : 'text-gray-700'
                        }`}
                      >
                        <span>{column.label}</span>
                        {sortField === column.key && (
                          sortDirection === 'asc' ? (
                            <ArrowUpIcon className="h-3 w-3" />
                          ) : (
                            <ArrowDownIcon className="h-3 w-3" />
                          )
                        )}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Active Filters Display */}
        {(searchTerm || showFilters) && (
          <div className="flex items-center space-x-2 pt-2 border-t border-gray-100">
            <span className="text-sm text-gray-500">Active:</span>
            {searchTerm && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Search: "{searchTerm}"
                <button
                  onClick={() => setSearchTerm('')}
                  className="ml-1 text-blue-600 hover:text-blue-800"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              Sort: {config.columns.find(col => col.key === sortField)?.label} ({sortDirection === 'asc' ? 'A-Z' : 'Z-A'})
            </span>
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              View: {viewSettings.mode.charAt(0).toUpperCase() + viewSettings.mode.slice(1)}
            </span>
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              Density: {viewSettings.density === 'compact' ? 'Compact' : 'Comfortable'}
            </span>
          </div>
        )}

        {/* Filters Dropdown */}
        {showFilters && config.filters && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg border">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {config.filters.map((filter) => (
                <div key={filter.name}>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {filter.label}
                  </label>
                  {filter.type === 'select' ? (
                    <select
                      value={filters[filter.name] || ''}
                      onChange={(e) => handleFilterChange(filter.name, e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    >
                      {filter.options?.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  ) : (
                    <input
                      type={filter.type}
                      value={filters[filter.name] || ''}
                      onChange={(e) => handleFilterChange(filter.name, e.target.value)}
                      placeholder={filter.placeholder}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* Bulk Actions */}
      {config.enableBulkActions && selectedClients.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <span className="text-sm font-medium text-blue-900">
                {selectedClients.length} client{selectedClients.length !== 1 ? 's' : ''} selected
              </span>
            </div>
            <div className="flex items-center space-x-2">
              {config.bulkActions?.map((action) => (
                <button
                  key={action.action}
                  onClick={() => {
                    if (action.requiresConfirmation) {
                      if (window.confirm(action.confirmationMessage || `Are you sure you want to ${action.action} the selected clients?`)) {
                        handleBulkAction(action.action, selectedClients)
                      }
                    } else {
                      handleBulkAction(action.action, selectedClients)
                    }
                  }}
                  className={`inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md ${
                    action.variant === 'danger'
                      ? 'text-red-700 bg-red-100 hover:bg-red-200'
                      : action.variant === 'warning'
                      ? 'text-yellow-700 bg-yellow-100 hover:bg-yellow-200'
                      : action.variant === 'success'
                      ? 'text-green-700 bg-green-100 hover:bg-green-200'
                      : 'text-blue-700 bg-blue-100 hover:bg-blue-200'
                  }`}
                >
                  {action.label}
                </button>
              ))}
              <button
                onClick={() => setSelectedClients([])}
                className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Clear Selection
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Clients Display */}
      {viewSettings.mode === 'list' ? (
        renderTableView()
      ) : viewSettings.mode === 'grid' ? (
        renderGridView()
      ) : (
        renderCardsView()
      )}



      {/* Create Modal */}
      <ClientModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreate}
        title="Create Client"
        fields={config.fields}
        layout={config.formLayout}
      />

      {/* Edit Modal */}
      <ClientModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false)
          setEditingClient(null)
        }}
        onSubmit={(data) => editingClient && handleUpdate(editingClient.id, data)}
        title="Edit Client"
        initialData={editingClient}
        fields={config.fields}
        layout={config.formLayout}
      />
    </div>
  )
}
