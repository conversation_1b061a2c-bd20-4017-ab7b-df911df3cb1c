'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Elements } from '@stripe/react-stripe-js'
import { StripePaymentForm } from './stripe-payment-form'
import axios from 'axios'
import {
  XMarkIcon,
  CreditCardIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  BanknotesIcon,
  BuildingLibraryIcon,
  DevicePhoneMobileIcon,
  GlobeAltIcon,
  ChevronDownIcon,
  ShieldCheckIcon,
  LockClosedIcon,
  EnvelopeIcon,
  TagIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline'
import { stripePromise, CURRENCIES, DEFAULT_CURRENCY } from '@/lib/stripe'
import { paymentFormSchema, PaymentFormData, PROMO_CODES } from '@/lib/payment-schema'
import { transformPaymentFormData, validatePaymentMethodFields, calculateProcessingFee } from '@/lib/payment-utils'

interface EnhancedPaymentFormModalProps {
  isOpen: boolean
  onClose: () => void
  invoice: {
    id: string | number
    totalAmount: number
    description?: string
    status?: string
    dueDate?: string
  }
  project?: {
    id: string | number
    name: string
  }
  client?: {
    id: string | number
    companyName: string
    contactEmail?: string
  }
  onSubmit: (paymentData: any) => void
}

interface PaymentMethod {
  id: string
  name: string
  icon: React.ComponentType<{ className?: string }>
  description: string
  processingFee?: string
  processingTime: string
  requiresStripe?: boolean
  fields: string[]
}

const paymentMethods: PaymentMethod[] = [
  {
    id: 'stripe_card',
    name: 'Credit/Debit Card',
    icon: CreditCardIcon,
    description: 'Visa, Mastercard, American Express',
    processingFee: '2.9% + $0.30',
    processingTime: 'Instant',
    requiresStripe: true,
    fields: ['cardNumber', 'expiryDate', 'cvv', 'cardholderName']
  },
  {
    id: 'paypal',
    name: 'PayPal',
    icon: GlobeAltIcon,
    description: 'PayPal account or guest checkout',
    processingFee: '2.9% + $0.30',
    processingTime: 'Instant',
    fields: ['paypalEmail']
  },
  {
    id: 'bank_transfer',
    name: 'Bank Transfer',
    icon: BuildingLibraryIcon,
    description: 'Direct bank account transfer',
    processingFee: '$0.80',
    processingTime: '1-3 business days',
    fields: ['bankName', 'accountNumber', 'routingNumber', 'accountHolderName']
  },
  {
    id: 'wire_transfer',
    name: 'Wire Transfer',
    icon: BuildingLibraryIcon,
    description: 'International wire transfer',
    processingFee: '$15-25',
    processingTime: '1-5 business days',
    fields: ['bankName', 'swiftCode', 'accountNumber', 'accountHolderName', 'bankAddress']
  },
  {
    id: 'ach_transfer',
    name: 'ACH Transfer',
    icon: BuildingLibraryIcon,
    description: 'Automated Clearing House',
    processingFee: '$0.80',
    processingTime: '3-5 business days',
    fields: ['bankName', 'accountNumber', 'routingNumber', 'accountHolderName']
  },
  {
    id: 'apple_pay',
    name: 'Apple Pay',
    icon: DevicePhoneMobileIcon,
    description: 'Apple Pay digital wallet',
    processingFee: '2.9% + $0.30',
    processingTime: 'Instant',
    requiresStripe: true,
    fields: []
  },
  {
    id: 'google_pay',
    name: 'Google Pay',
    icon: DevicePhoneMobileIcon,
    description: 'Google Pay digital wallet',
    processingFee: '2.9% + $0.30',
    processingTime: 'Instant',
    requiresStripe: true,
    fields: []
  },
  {
    id: 'check',
    name: 'Check',
    icon: BanknotesIcon,
    description: 'Physical or electronic check',
    processingTime: '3-7 business days',
    fields: ['checkNumber', 'bankName']
  },
  {
    id: 'cash',
    name: 'Cash',
    icon: BanknotesIcon,
    description: 'Cash payment in person',
    processingTime: 'Instant',
    fields: []
  }
]

export function EnhancedPaymentFormModal({ 
  isOpen, 
  onClose, 
  invoice, 
  project, 
  client, 
  onSubmit 
}: EnhancedPaymentFormModalProps) {
  const [loading, setLoading] = useState(false)
  const [remainingAmount, setRemainingAmount] = useState(0)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod>(paymentMethods[0])
  const [appliedPromo, setAppliedPromo] = useState<any>(null)
  const [clientSecret, setClientSecret] = useState('')

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
    reset
  } = useForm({
    defaultValues: {
      amount: 0,
      currency: DEFAULT_CURRENCY,
      paymentMethod: 'stripe_card',
      paymentDate: new Date().toISOString().split('T')[0],
      notes: '',
      promoCode: '',
      emailReceipt: false,
      receiptEmail: client?.contactEmail || '',
      termsAccepted: false
    }
  })

  const watchedAmount = watch('amount')
  const watchedCurrency = watch('currency')
  const watchedPromoCode = watch('promoCode')

  // Calculate remaining amount
  useEffect(() => {
    const fetchRemainingAmount = async () => {
      try {
        const response = await fetch(`/api/admin/invoices-new/${invoice.id}/payments`)
        if (response.ok) {
          const data = await response.json()
          const totalPaid = data.data?.reduce((sum: number, payment: any) => sum + Number(payment.amount), 0) || 0
          setRemainingAmount(Number(invoice.totalAmount) - totalPaid)
          setValue('amount', Number(invoice.totalAmount) - totalPaid)
        } else {
          setRemainingAmount(Number(invoice.totalAmount))
          setValue('amount', Number(invoice.totalAmount))
        }
      } catch (error) {
        console.error('Error fetching payments:', error)
        setRemainingAmount(Number(invoice.totalAmount))
        setValue('amount', Number(invoice.totalAmount))
      }
    }

    if (isOpen) {
      fetchRemainingAmount()
    }
  }, [isOpen, invoice.id, invoice.totalAmount, setValue])

  // Handle promo code validation
  useEffect(() => {
    if (watchedPromoCode) {
      const promo = PROMO_CODES.find(p => p.code === watchedPromoCode.toUpperCase())
      setAppliedPromo(promo || null)
    } else {
      setAppliedPromo(null)
    }
  }, [watchedPromoCode])

  const calculateTotal = () => {
    let total = watchedAmount || 0
    
    if (appliedPromo) {
      if (appliedPromo.type === 'percentage') {
        total = total * (1 - appliedPromo.discount / 100)
      } else {
        total = Math.max(0, total - appliedPromo.discount)
      }
    }

    // Add processing fee
    if (selectedPaymentMethod.processingFee) {
      const fee = selectedPaymentMethod.processingFee
      if (fee.includes('%')) {
        const percentage = parseFloat(fee.match(/(\d+\.?\d*)%/)?.[1] || '0')
        const fixed = parseFloat(fee.match(/\$(\d+\.?\d*)/)?.[1] || '0')
        total = total * (1 + percentage / 100) + fixed
      } else if (fee.includes('$')) {
        const fixed = parseFloat(fee.match(/\$(\d+\.?\d*)/)?.[1] || '0')
        total = total + fixed
      }
    }

    return total
  }

  const formatCurrency = (amount: number, currency: string = DEFAULT_CURRENCY) => {
    const currencyData = CURRENCIES.find(c => c.code === currency)
    return `${currencyData?.symbol || '$'}${amount.toFixed(2)}`
  }

  const handleFormSubmit = async (data: any) => {
    setLoading(true)
    try {
      // For Stripe payments, create payment intent first
      if (selectedPaymentMethod.requiresStripe) {
        const response = await fetch('/api/stripe/create-payment-intent', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            amount: calculateTotal(),
            currency: data.currency,
            metadata: {
              invoiceId: invoice.id,
              projectId: project?.id,
              clientId: client?.id,
              paymentMethod: selectedPaymentMethod.id,
              promoCode: appliedPromo?.code || '',
              discount: appliedPromo ? (appliedPromo.type === 'percentage'
                ? (data.amount * appliedPromo.discount / 100)
                : appliedPromo.discount) : 0,
              emailReceipt: data.emailReceipt.toString(),
              receiptEmail: data.receiptEmail || '',
              paymentDetails: JSON.stringify({}), // Will be updated when form is submitted
            },
          }),
        })

        if (!response.ok) {
          throw new Error('Failed to create payment intent')
        }

        const { clientSecret } = await response.json()
        setClientSecret(clientSecret)

        // For now, we'll handle Stripe payments separately
        // The actual payment confirmation will happen in the Stripe component
        return
      }

      // Collect payment method specific details from form
      const formElement = document.querySelector('form')
      const formFieldData: any = { ...data }

      if (formElement) {
        const formData = new FormData(formElement)
        selectedPaymentMethod.fields.forEach((field: string) => {
          const value = formData.get(field) as string
          if (value) {
            formFieldData[field] = value
          }
        })
      }

      // Validate payment method specific fields
      const validation = validatePaymentMethodFields(selectedPaymentMethod, formFieldData)
      if (!validation.isValid) {
        alert(`Please fix the following errors:\n${validation.errors.join('\n')}`)
        return
      }

      // Transform form data to payment API format
      const paymentData = transformPaymentFormData(
        { ...formFieldData, amount: calculateTotal() },
        selectedPaymentMethod,
        appliedPromo,
        invoice,
        project,
        client
      )

      // Add processing fee
      paymentData.processingFee = calculateProcessingFee(selectedPaymentMethod.id, paymentData.amount)

      await onSubmit(paymentData)
      reset()
      onClose()
    } catch (error) {
      console.error('Payment submission error:', error)
      alert('Failed to process payment. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex min-h-screen items-center justify-center p-4">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="relative w-full max-w-4xl bg-white rounded-xl shadow-2xl max-h-[90vh] overflow-y-auto"
          >
            {/* Header */}
            <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 rounded-t-xl">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">Secure Payment</h2>
                  <p className="text-sm text-gray-600 mt-1">
                    Invoice #{invoice.id} - {project?.name} ({client?.companyName})
                  </p>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <XMarkIcon className="h-6 w-6 text-gray-400" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6">
              <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
                {/* Payment Summary */}
                <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-6 border border-purple-200">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Summary</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Invoice Amount:</span>
                      <span className="font-medium">{formatCurrency(invoice.totalAmount, watchedCurrency)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Amount Due:</span>
                      <span className="font-medium">{formatCurrency(remainingAmount, watchedCurrency)}</span>
                    </div>
                    {appliedPromo && (
                      <div className="flex justify-between text-green-600">
                        <span>Discount ({appliedPromo.code}):</span>
                        <span>
                          -{appliedPromo.type === 'percentage' 
                            ? `${appliedPromo.discount}%` 
                            : formatCurrency(appliedPromo.discount, watchedCurrency)
                          }
                        </span>
                      </div>
                    )}
                    <div className="border-t pt-2 flex justify-between text-lg font-bold">
                      <span>Total:</span>
                      <span className="text-purple-600">{formatCurrency(calculateTotal(), watchedCurrency)}</span>
                    </div>
                  </div>
                </div>

                {/* Amount and Currency */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Payment Amount *
                    </label>
                    <Controller
                      name="amount"
                      control={control}
                      render={({ field }) => (
                        <div className="relative">
                          <CurrencyDollarIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                          <input
                            {...field}
                            type="number"
                            step="0.01"
                            min="0"
                            max={remainingAmount}
                            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                            placeholder="0.00"
                          />
                        </div>
                      )}
                    />
                    {errors.amount && (
                      <p className="mt-1 text-sm text-red-600">{errors.amount.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Currency
                    </label>
                    <Controller
                      name="currency"
                      control={control}
                      render={({ field }) => (
                        <select
                          {...field}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        >
                          {CURRENCIES.map((currency) => (
                            <option key={currency.code} value={currency.code}>
                              {currency.symbol} {currency.name} ({currency.code})
                            </option>
                          ))}
                        </select>
                      )}
                    />
                  </div>
                </div>

                {/* Payment Method Selection */}
                <div className="relative payment-method-dropdown">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Payment Method *
                  </label>
                  <div className="relative">
                    <button
                      type="button"
                      onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                      className="relative w-full cursor-pointer rounded-lg border border-gray-300 bg-white py-3 pl-4 pr-10 text-left shadow-sm focus:border-purple-500 focus:outline-none focus:ring-1 focus:ring-purple-500"
                    >
                      <div className="flex items-center">
                        <selectedPaymentMethod.icon className="h-5 w-5 text-gray-400 mr-3" />
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <span className="block truncate font-medium text-gray-900">
                              {selectedPaymentMethod.name}
                            </span>
                            {selectedPaymentMethod.processingFee && (
                              <span className="text-sm text-gray-500 ml-2">
                                {selectedPaymentMethod.processingFee}
                              </span>
                            )}
                          </div>
                          <span className="block truncate text-sm text-gray-500">
                            {selectedPaymentMethod.description} • {selectedPaymentMethod.processingTime}
                          </span>
                        </div>
                      </div>
                      <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                        <ChevronDownIcon
                          className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${
                            isDropdownOpen ? 'rotate-180' : ''
                          }`}
                        />
                      </span>
                    </button>

                    {/* Dropdown Options */}
                    {isDropdownOpen && (
                      <div className="absolute z-10 mt-1 w-full rounded-lg bg-white shadow-lg border border-gray-200 max-h-60 overflow-auto">
                        {paymentMethods.map((method) => (
                          <button
                            key={method.id}
                            type="button"
                            onClick={() => {
                              setSelectedPaymentMethod(method)
                              setIsDropdownOpen(false)
                            }}
                            className={`w-full px-4 py-3 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none ${
                              selectedPaymentMethod.id === method.id ? 'bg-purple-50 border-l-4 border-purple-500' : ''
                            }`}
                          >
                            <div className="flex items-center">
                              <method.icon className="h-5 w-5 text-gray-400 mr-3" />
                              <div className="flex-1">
                                <div className="flex items-center justify-between">
                                  <span className="font-medium text-gray-900">{method.name}</span>
                                  {method.processingFee && (
                                    <span className="text-sm text-gray-500">{method.processingFee}</span>
                                  )}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {method.description} • {method.processingTime}
                                </div>
                              </div>
                            </div>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {/* Stripe Payment Integration */}
                {selectedPaymentMethod.requiresStripe && clientSecret && (
                  <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">
                      Secure Card Payment
                    </h4>
                    <Elements
                      stripe={stripePromise}
                      options={{
                        clientSecret,
                        appearance: {
                          theme: 'stripe',
                          variables: {
                            colorPrimary: '#7c3aed',
                          },
                        },
                      }}
                    >
                      <StripePaymentForm
                        clientSecret={clientSecret}
                        amount={calculateTotal()}
                        currency={watchedCurrency}
                        onSuccess={(paymentIntent) => {
                          console.log('Payment succeeded:', paymentIntent)
                          onClose()
                          // Refresh the payments list
                          window.location.reload()
                        }}
                        onError={(error) => {
                          console.error('Payment failed:', error)
                          alert(`Payment failed: ${error}`)
                        }}
                      />
                    </Elements>
                  </div>
                )}

                {/* Dynamic Payment Method Fields */}
                {((selectedPaymentMethod.requiresStripe && !clientSecret) || !selectedPaymentMethod.requiresStripe) && selectedPaymentMethod.fields.length > 0 && (
                  <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">
                      {selectedPaymentMethod.name} Details
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {selectedPaymentMethod.fields.map((field) => (
                        <div key={field}>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            {field === 'cardNumber' && 'Card Number *'}
                            {field === 'expiryDate' && 'Expiry Date *'}
                            {field === 'cvv' && 'CVV *'}
                            {field === 'cardholderName' && 'Cardholder Name *'}
                            {field === 'paypalEmail' && 'PayPal Email'}
                            {field === 'bankName' && 'Bank Name *'}
                            {field === 'accountNumber' && 'Account Number *'}
                            {field === 'routingNumber' && 'Routing Number *'}
                            {field === 'accountHolderName' && 'Account Holder Name *'}
                            {field === 'swiftCode' && 'SWIFT Code *'}
                            {field === 'bankAddress' && 'Bank Address *'}
                            {field === 'checkNumber' && 'Check Number *'}
                          </label>
                          <input
                            name={field}
                            type={field === 'paypalEmail' ? 'email' : 'text'}
                            placeholder={
                              field === 'cardNumber' ? '1234 5678 9012 3456' :
                              field === 'expiryDate' ? 'MM/YY' :
                              field === 'cvv' ? '123' :
                              field === 'paypalEmail' ? '<EMAIL>' :
                              ''
                            }
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                            required={field !== 'paypalEmail'}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Promo Code */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <TagIcon className="inline h-4 w-4 mr-1" />
                    Promo Code (Optional)
                  </label>
                  <Controller
                    name="promoCode"
                    control={control}
                    render={({ field }) => (
                      <div className="relative">
                        <input
                          {...field}
                          type="text"
                          placeholder="Enter promo code"
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent uppercase"
                        />
                        {appliedPromo && (
                          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                            <CheckCircleIcon className="h-5 w-5 text-green-500" />
                          </div>
                        )}
                      </div>
                    )}
                  />
                  {appliedPromo && (
                    <p className="mt-1 text-sm text-green-600">
                      ✓ {appliedPromo.type === 'percentage'
                        ? `${appliedPromo.discount}% discount applied`
                        : `$${appliedPromo.discount} discount applied`
                      }
                    </p>
                  )}
                </div>

                {/* Payment Date and Notes */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Payment Date *
                    </label>
                    <Controller
                      name="paymentDate"
                      control={control}
                      render={({ field }) => (
                        <div className="relative">
                          <CalendarIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                          <input
                            {...field}
                            type="date"
                            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                          />
                        </div>
                      )}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Notes (Optional)
                    </label>
                    <Controller
                      name="notes"
                      control={control}
                      render={({ field }) => (
                        <textarea
                          {...field}
                          rows={3}
                          placeholder="Add payment notes..."
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                        />
                      )}
                    />
                  </div>
                </div>

                {/* Email Receipt */}
                <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                  <div className="flex items-start space-x-3">
                    <Controller
                      name="emailReceipt"
                      control={control}
                      render={({ field }) => (
                        <input
                          {...field}
                          type="checkbox"
                          className="mt-1 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                        />
                      )}
                    />
                    <div className="flex-1">
                      <label className="text-sm font-medium text-gray-900">
                        Email payment receipt
                      </label>
                      <p className="text-sm text-gray-600">
                        Send a payment confirmation to the client's email address
                      </p>
                      {watch('emailReceipt') && (
                        <div className="mt-2">
                          <Controller
                            name="receiptEmail"
                            control={control}
                            render={({ field }) => (
                              <div className="relative">
                                <EnvelopeIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                <input
                                  {...field}
                                  type="email"
                                  placeholder="<EMAIL>"
                                  className="w-full pl-9 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm"
                                />
                              </div>
                            )}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Security Badges */}
                <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <div className="flex items-center justify-center space-x-6">
                    <div className="flex items-center space-x-2">
                      <ShieldCheckIcon className="h-5 w-5 text-green-600" />
                      <span className="text-sm font-medium text-gray-700">SSL Secured</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <LockClosedIcon className="h-5 w-5 text-green-600" />
                      <span className="text-sm font-medium text-gray-700">PCI-DSS Compliant</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircleIcon className="h-5 w-5 text-green-600" />
                      <span className="text-sm font-medium text-gray-700">256-bit Encryption</span>
                    </div>
                  </div>
                </div>

                {/* Terms and Conditions */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <Controller
                      name="termsAccepted"
                      control={control}
                      render={({ field }) => (
                        <input
                          {...field}
                          type="checkbox"
                          className="mt-1 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                        />
                      )}
                    />
                    <div className="flex-1">
                      <label className="text-sm font-medium text-gray-900">
                        I accept the Terms & Conditions *
                      </label>
                      <p className="text-sm text-gray-600 mt-1">
                        By proceeding with this payment, you agree to our{' '}
                        <a href="#" className="text-purple-600 hover:text-purple-700 underline">
                          Terms of Service
                        </a>{' '}
                        and{' '}
                        <a href="#" className="text-purple-600 hover:text-purple-700 underline">
                          Privacy Policy
                        </a>
                      </p>
                    </div>
                  </div>
                  {errors.termsAccepted && (
                    <p className="mt-2 text-sm text-red-600">{errors.termsAccepted.message}</p>
                  )}
                </div>

                {/* Submit Button - Only show for non-Stripe payments */}
                {!selectedPaymentMethod.requiresStripe && (
                  <div className="flex items-center justify-between pt-6 border-t border-gray-200">
                    <button
                      type="button"
                      onClick={onClose}
                      className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={loading}
                      className="px-8 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-lg hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                    >
                      {loading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                          <span>Processing...</span>
                        </>
                      ) : (
                        <>
                          <LockClosedIcon className="h-4 w-4" />
                          <span>Process Payment {formatCurrency(calculateTotal(), watchedCurrency)}</span>
                        </>
                      )}
                    </button>
                  </div>
                )}

                {/* For Stripe payments, show setup button */}
                {selectedPaymentMethod.requiresStripe && !clientSecret && (
                  <div className="flex items-center justify-between pt-6 border-t border-gray-200">
                    <button
                      type="button"
                      onClick={onClose}
                      className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={loading}
                      className="px-8 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-lg hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                    >
                      {loading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                          <span>Setting up payment...</span>
                        </>
                      ) : (
                        <>
                          <CreditCardIcon className="h-4 w-4" />
                          <span>Setup Secure Payment</span>
                        </>
                      )}
                    </button>
                  </div>
                )}

                {/* For Stripe payments with client secret, show cancel only */}
                {selectedPaymentMethod.requiresStripe && clientSecret && (
                  <div className="flex items-center justify-center pt-6 border-t border-gray-200">
                    <button
                      type="button"
                      onClick={onClose}
                      className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                    >
                      Cancel
                    </button>
                  </div>
                )}
              </form>
            </div>
          </motion.div>
        </div>
      </div>
    </AnimatePresence>
  )
}
