import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'
import { prisma } from '@/lib/prisma'

export interface AuthenticatedUser {
  id: string
  email: string
  role: 'ADMIN' | 'USER' | 'CLIENT'
  firstName?: string
  lastName?: string
}

export async function authenticateRequest(request: NextRequest): Promise<AuthenticatedUser | null> {
  try {
    const token = await getToken({ 
      req: request, 
      secret: process.env.NEXTAUTH_SECRET 
    })

    if (!token || !token.sub) {
      return null
    }

    // Get user from database to ensure they still exist and get latest role
    const user = await prisma.user.findUnique({
      where: { id: token.sub },
      select: {
        id: true,
        email: true,
        role: true,
        firstName: true,
        lastName: true
      }
    })

    if (!user) {
      return null
    }

    return {
      id: user.id,
      email: user.email,
      role: user.role as 'ADMIN' | 'USER' | 'CLIENT',
      firstName: user.firstName || undefined,
      lastName: user.lastName || undefined
    }
  } catch (error) {
    console.error('Authentication error:', error)
    return null
  }
}

export async function requireAuth(request: NextRequest): Promise<AuthenticatedUser> {
  const user = await authenticateRequest(request)
  
  if (!user) {
    throw new Error('Authentication required')
  }

  return user
}

export async function requireAdmin(request: NextRequest): Promise<AuthenticatedUser> {
  const user = await requireAuth(request)
  
  if (user.role !== 'ADMIN') {
    throw new Error('Admin access required')
  }

  return user
}

export async function requireRole(
  request: NextRequest, 
  allowedRoles: ('ADMIN' | 'USER' | 'CLIENT')[]
): Promise<AuthenticatedUser> {
  const user = await requireAuth(request)
  
  if (!allowedRoles.includes(user.role)) {
    throw new Error(`Access denied. Required roles: ${allowedRoles.join(', ')}`)
  }

  return user
}

// Middleware for protecting API routes
export function withAuth(
  handler: (request: NextRequest, user: AuthenticatedUser, context?: any) => Promise<NextResponse>,
  options: {
    requireAdmin?: boolean
    allowedRoles?: ('ADMIN' | 'USER' | 'CLIENT')[]
  } = {}
) {
  return async (request: NextRequest, context?: any): Promise<NextResponse> => {
    try {
      let user: AuthenticatedUser

      if (options.requireAdmin) {
        user = await requireAdmin(request)
      } else if (options.allowedRoles) {
        user = await requireRole(request, options.allowedRoles)
      } else {
        user = await requireAuth(request)
      }

      return await handler(request, user, context)
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Authentication failed'
      
      if (message.includes('Authentication required')) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        )
      }
      
      if (message.includes('Admin access required') || message.includes('Access denied')) {
        return NextResponse.json(
          { success: false, error: 'Insufficient permissions' },
          { status: 403 }
        )
      }

      return NextResponse.json(
        { success: false, error: 'Authentication failed' },
        { status: 401 }
      )
    }
  }
}

// Session management utilities
export async function createUserSession(userId: string): Promise<string> {
  // This would typically create a session token
  // For now, we rely on NextAuth's session management
  return userId
}

export async function invalidateUserSession(userId: string): Promise<void> {
  // This would typically invalidate all sessions for a user
  // For now, we rely on NextAuth's session management
  console.log(`Invalidating sessions for user: ${userId}`)
}

// Rate limiting utilities
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

export function rateLimit(
  identifier: string,
  maxRequests: number = 100,
  windowMs: number = 15 * 60 * 1000 // 15 minutes
): boolean {
  const now = Date.now()
  const windowStart = now - windowMs

  // Clean up old entries
  for (const [key, value] of rateLimitMap.entries()) {
    if (value.resetTime < windowStart) {
      rateLimitMap.delete(key)
    }
  }

  const current = rateLimitMap.get(identifier)
  
  if (!current) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now })
    return true
  }

  if (current.resetTime < windowStart) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now })
    return true
  }

  if (current.count >= maxRequests) {
    return false
  }

  current.count++
  return true
}

export function withRateLimit(
  handler: (request: NextRequest, context?: any) => Promise<NextResponse>,
  options: {
    maxRequests?: number
    windowMs?: number
    keyGenerator?: (request: NextRequest) => string
  } = {}
) {
  const {
    maxRequests = 100,
    windowMs = 15 * 60 * 1000,
    keyGenerator = (req) => req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'anonymous'
  } = options

  return async (request: NextRequest, context?: any): Promise<NextResponse> => {
    const identifier = keyGenerator(request)
    
    if (!rateLimit(identifier, maxRequests, windowMs)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Too many requests. Please try again later.' 
        },
        { status: 429 }
      )
    }

    return await handler(request, context)
  }
}

// Audit logging
export async function logUserAction(
  userId: string,
  action: string,
  details?: Record<string, any>
): Promise<void> {
  try {
    // In a real application, you might want to create an audit log table
    console.log('User action:', {
      userId,
      action,
      details,
      timestamp: new Date().toISOString()
    })
    
    // You could also store this in the database:
    // await prisma.auditLog.create({
    //   data: {
    //     userId,
    //     action,
    //     details: JSON.stringify(details),
    //     timestamp: new Date()
    //   }
    // })
  } catch (error) {
    console.error('Failed to log user action:', error)
  }
}
